"""
Vehicle class for the Feroke Bridge Traffic Simulation
Handles vehicle movement, physics, and rendering
"""

import pygame
import random
from utils import *

class Vehicle:
    """
    Represents a single vehicle in the simulation with realistic physics
    """
    
    def __init__(self, vehicle_type, side, spawn_x):
        """
        Initialize a new vehicle
        
        Args:
            vehicle_type: Type of vehicle (bike, car, auto, truck, bus)
            side: Which side of bridge (LEFT or RIGHT)
            spawn_x: X coordinate where vehicle spawns
        """
        self.type = vehicle_type
        self.side = side
        self.size = VEHICLE_SIZES[vehicle_type]
        self.color = VEHICLE_COLORS[vehicle_type]
        self.weight = VEHICLE_WEIGHTS[vehicle_type]
        self.max_speed = VEHICLE_SPEEDS[vehicle_type]
        
        # Position and movement
        self.pos = [spawn_x, VEHICLE_Y - self.size[1] // 2]
        self.velocity = 0
        self.target_velocity = self.max_speed
        self.acceleration = 120  # pixels per second squared
        self.deceleration = 200
        
        # State tracking
        self.is_moving = False
        self.is_on_bridge = False
        self.wait_time = 0
        self.total_wait_time = 0
        
        # Direction: 1 for right (LEFT side vehicles), -1 for left (RIGHT side vehicles)
        self.direction = 1 if side == "LEFT" else -1
        
        # Safety distance for following other vehicles
        self.safety_distance = 15 + random.randint(0, 10)
        
    def update(self, dt, signal_state, other_vehicles):
        """
        Update vehicle position and state
        
        Args:
            dt: Delta time in seconds
            signal_state: Current traffic signal state
            other_vehicles: List of other vehicles for collision detection
        """
        # Determine if this vehicle should be moving based on signal
        should_move = self._should_move(signal_state)
        
        # Check for obstacles ahead
        obstacle_ahead = self._check_obstacles(other_vehicles)
        
        # Update target velocity based on conditions
        if should_move and not obstacle_ahead:
            self.target_velocity = self.max_speed
            self.is_moving = True
        else:
            self.target_velocity = 0
            self.is_moving = False
            
        # Apply physics - smooth acceleration/deceleration
        if self.target_velocity > self.velocity:
            self.velocity = apply_acceleration(
                self.velocity, self.target_velocity, self.acceleration, dt
            )
        else:
            self.velocity = apply_acceleration(
                self.velocity, self.target_velocity, self.deceleration, dt
            )
        
        # Update position
        self.pos[0] += self.velocity * self.direction * dt
        
        # Update wait time if not moving
        if not self.is_moving:
            self.wait_time += dt
            self.total_wait_time += dt
        else:
            self.wait_time = 0
            
        # Check if vehicle is on bridge
        self._update_bridge_status()
        
    def _should_move(self, signal_state):
        """Determine if vehicle should move based on signal state"""
        if self.side == "LEFT":
            # Left side vehicles move when signal is LEFT_GREEN
            if signal_state in ["LEFT_GREEN"]:
                return True
            # Also move if already on bridge during yellow
            elif signal_state == "LEFT_YELLOW" and self.is_on_bridge:
                return True
        else:
            # Right side vehicles move when signal is RIGHT_GREEN
            if signal_state in ["RIGHT_GREEN"]:
                return True
            # Also move if already on bridge during yellow
            elif signal_state == "RIGHT_YELLOW" and self.is_on_bridge:
                return True
                
        return False
        
    def _check_obstacles(self, other_vehicles):
        """Check for vehicles ahead that would cause collision"""
        for other in other_vehicles:
            if other == self or other.pos[1] != self.pos[1]:
                continue
                
            # Calculate distance to other vehicle
            if self.direction > 0:  # Moving right
                distance = other.pos[0] - (self.pos[0] + self.size[0])
                if 0 < distance < self.safety_distance + other.size[0]:
                    return True
            else:  # Moving left
                distance = (self.pos[0]) - (other.pos[0] + other.size[0])
                if 0 < distance < self.safety_distance + other.size[0]:
                    return True
                    
        return False
        
    def _update_bridge_status(self):
        """Update whether vehicle is currently on the bridge"""
        vehicle_left = self.pos[0]
        vehicle_right = self.pos[0] + self.size[0]
        
        # Vehicle is on bridge if any part overlaps with bridge area
        self.is_on_bridge = (
            vehicle_left < BRIDGE_X + BRIDGE_WIDTH and 
            vehicle_right > BRIDGE_X
        )
        
    def should_be_removed(self):
        """Check if vehicle should be removed from simulation"""
        if self.side == "LEFT":
            # Remove if vehicle has passed the right edge
            return self.pos[0] > SCREEN_WIDTH + 50
        else:
            # Remove if vehicle has passed the left edge
            return self.pos[0] + self.size[0] < -50
            
    def get_stop_position(self):
        """Get the position where this vehicle should stop at red light"""
        if self.side == "LEFT":
            return LEFT_STOP_LINE - self.size[0]
        else:
            return RIGHT_STOP_LINE
            
    def is_in_detection_zone(self):
        """Check if vehicle is in the AI detection zone"""
        vehicle_center = self.pos[0] + self.size[0] // 2
        
        if self.side == "LEFT":
            return LEFT_DETECTION_ZONE[0] <= vehicle_center <= LEFT_DETECTION_ZONE[1]
        else:
            return RIGHT_DETECTION_ZONE[0] <= vehicle_center <= RIGHT_DETECTION_ZONE[1]
            
    def render(self, surface):
        """
        Render the vehicle on the given surface
        
        Args:
            surface: Pygame surface to draw on
        """
        # Create vehicle rectangle
        rect = pygame.Rect(self.pos[0], self.pos[1], self.size[0], self.size[1])
        
        # Add slight transparency if waiting
        alpha = 200 if not self.is_moving else 255
        
        # Create a surface with per-pixel alpha for transparency
        vehicle_surface = pygame.Surface(self.size, pygame.SRCALPHA)
        vehicle_surface.fill((*self.color, alpha))
        
        # Draw vehicle body
        pygame.draw.rect(vehicle_surface, (*self.color, alpha), 
                        (0, 0, self.size[0], self.size[1]), border_radius=3)
        
        # Add vehicle type indicator (small rectangle on top)
        indicator_color = (255, 255, 255, alpha)
        indicator_size = (max(4, self.size[0] // 6), 2)
        indicator_pos = (self.size[0] // 2 - indicator_size[0] // 2, 2)
        pygame.draw.rect(vehicle_surface, indicator_color, 
                        (*indicator_pos, *indicator_size))
        
        # Blit the vehicle surface to the main surface
        surface.blit(vehicle_surface, self.pos)
        
        # Draw direction indicator (small arrow)
        self._draw_direction_indicator(surface)
        
    def _draw_direction_indicator(self, surface):
        """Draw a small arrow indicating vehicle direction"""
        arrow_size = 6
        arrow_y = self.pos[1] + self.size[1] // 2
        
        if self.direction > 0:  # Moving right
            arrow_x = self.pos[0] + self.size[0] - 8
            points = [
                (arrow_x, arrow_y),
                (arrow_x - arrow_size, arrow_y - arrow_size // 2),
                (arrow_x - arrow_size, arrow_y + arrow_size // 2)
            ]
        else:  # Moving left
            arrow_x = self.pos[0] + 8
            points = [
                (arrow_x, arrow_y),
                (arrow_x + arrow_size, arrow_y - arrow_size // 2),
                (arrow_x + arrow_size, arrow_y + arrow_size // 2)
            ]
            
        pygame.draw.polygon(surface, (255, 255, 255), points)
        
    def get_info(self):
        """Get vehicle information for debugging/statistics"""
        return {
            'type': self.type,
            'side': self.side,
            'pos': self.pos.copy(),
            'velocity': self.velocity,
            'is_moving': self.is_moving,
            'is_on_bridge': self.is_on_bridge,
            'wait_time': self.wait_time,
            'total_wait_time': self.total_wait_time,
            'weight': self.weight
        }
