# Smart Autonomous AI-Driven Signal System for Feroke Bridge

A comprehensive traffic simulation demonstrating an AI-controlled dynamic signal mechanism for the Old Feroke Bridge in Kozhikode, Kerala. This prototype simulates intelligent traffic management for a narrow bridge that allows only one-way traffic at a time.

## 🌉 Project Overview

The Old Feroke Bridge connects Feroke and Ramanattukara across the Chaliyar River. Due to its narrow width, only vehicles from one direction can pass at a time. This simulation demonstrates how AI can optimize traffic flow by:

- **Dynamic Signal Timing**: AI calculates optimal green times based on real-time traffic conditions
- **Vehicle Type Recognition**: Different weights for bikes, cars, autos, trucks, and buses
- **Starvation Prevention**: Ensures no side waits too long regardless of traffic imbalance
- **Fairness Algorithm**: Balances efficiency with equitable access for both sides

## 🚀 Features

### Core Functionality
- ✅ **Real-time Traffic Simulation**: Vehicles spawn randomly and move with realistic physics
- ✅ **Enhanced AI Traffic Control**: Periodic intelligent decision-making with advanced analysis
- ✅ **Advanced Starvation Prevention**: Emergency intervention and maximum wait time limits
- ✅ **Interactive Testing**: Manual vehicle addition buttons for scenario testing
- ✅ **Visual Feedback**: Live statistics, AI decisions, and reasoning display
- ✅ **User Controls**: Pause, reset, manual override, and vehicle addition capabilities

### Vehicle System
- **5 Vehicle Types**: Bike, Car, Auto, Truck, Bus
- **Realistic Physics**: Smooth acceleration, deceleration, and queuing
- **Weight-based Priority**: PCU (Passenger Car Units) system
- **Smart Movement**: Collision detection and safe following distances

### AI Algorithm
- **Proportional Allocation**: Green time based on traffic demand
- **Aging Factor**: Gradual priority increase for waiting vehicles
- **Fairness Metrics**: Queue length and wait time balancing
- **Performance Tracking**: Comprehensive statistics and analysis

## 📋 Requirements

### System Requirements
- Python 3.10 or higher
- Windows, macOS, or Linux
- Minimum 4GB RAM
- Graphics card supporting 1280x720 resolution

### Dependencies
```bash
pip install pygame numpy
```

Optional for enhanced features:
```bash
pip install matplotlib  # For advanced charting (future enhancement)
```

## 🛠️ Installation

1. **Clone or Download** the project files
2. **Install Dependencies**:
   ```bash
   pip install pygame numpy
   ```
3. **Run the Simulation**:
   ```bash
   python main.py
   ```

## 🎮 Controls

### Keyboard Controls
| Key | Action |
|-----|--------|
| `SPACE` | Pause/Resume simulation |
| `R` | Reset simulation |
| `1` | Force left side green (manual override) |
| `2` | Force right side green (manual override) |
| `C` | Clear manual override |
| `ESC` | Exit simulation |

### Mouse Controls (NEW! 🆕)
| Action | Description |
|--------|-------------|
| **Click Vehicle Buttons** | Add manual vehicles for testing scenarios |
| **Left Side Buttons** | Add vehicles to Feroke side (Bike/Car/Truck) |
| **Right Side Buttons** | Add vehicles to Ramanattukara side (Bike/Car/Truck) |

> 💡 **Tip**: Use manual vehicle addition to test AI decision-making under different traffic scenarios!

## 🧠 Enhanced AI System (NEW! 🆕)

### Intelligent Periodic Decision-Making
The AI now makes decisions **every 8 seconds** instead of continuously, making it more realistic and efficient:

- **🔄 Periodic Analysis**: AI evaluates traffic every 8 seconds
- **⚡ Emergency Intervention**: Immediate response for extreme wait times (>90s)
- **🎯 Priority-based Decisions**: Smart prioritization based on traffic analysis
- **📊 Enhanced Reasoning**: Detailed explanation of each AI decision

### Advanced Features
- **🚨 Starvation Prevention**: Maximum wait time limits with emergency intervention
- **⚖️ Traffic Imbalance Detection**: Automatic adjustment for uneven traffic
- **🚛 Heavy Vehicle Priority**: Extra time allocation for trucks and buses
- **📈 Performance Tracking**: Real-time metrics and decision history

### AI Logic Explained

#### Dynamic Timing Algorithm

The enhanced AI calculates green times using advanced analysis:
```
G_left = clamp((C_L / (C_L + C_R)) * BASE_CYCLE + aging_factor * W_L, MIN_GREEN, MAX_GREEN)
```

Where:
- `C_L`, `C_R` = Weighted vehicle counts (PCU)
- `W_L`, `W_R` = Waiting times for starvation prevention
- `BASE_CYCLE` = Target cycle length (30 seconds)
- `MIN_GREEN` = Minimum green time (5 seconds)
- `MAX_GREEN` = Maximum green time (40 seconds)

### Vehicle Weights (PCU System)
- **Bike**: 0.5 PCU (small, fast)
- **Car**: 1.0 PCU (baseline)
- **Auto**: 1.2 PCU (slightly larger)
- **Truck**: 2.0 PCU (large, slow)
- **Bus**: 2.5 PCU (largest, slowest)

### Starvation Prevention
- Tracks waiting time for each side
- Applies priority boost when wait time > 60 seconds
- Uses aging factor for gradual priority increase
- Guarantees service within maximum delay

## 📊 Understanding the Display

### Main View
- **Left Side**: Feroke approach (vehicles move right)
- **Center**: Bridge with traffic signals
- **Right Side**: Ramanattukara approach (vehicles move left)
- **Bottom Panel**: Live statistics and controls

### Signal Lights
- **Green**: Side can proceed
- **Yellow**: Prepare to stop (3 seconds)
- **Red**: Must stop and wait
- **All Red**: Safety buffer between phases (1 second)

### Statistics Panel
- **Vehicle Counts**: Spawned, completed, and current queue
- **Wait Times**: Average and current waiting periods
- **AI Status**: Decision reasoning and starvation risk
- **Performance**: FPS, simulation time, and metrics

## 🔧 Configuration

Key parameters can be adjusted in `utils.py`:

```python
# Timing Constants
MIN_GREEN = 5          # Minimum green time (seconds)
MAX_GREEN = 40         # Maximum green time (seconds)
STARVATION_LIMIT = 60  # Starvation threshold (seconds)

# Vehicle Spawn Rates
SPAWN_PROBABILITIES = {
    "bike": 0.3,       # 30% chance per second
    "car": 0.4,        # 40% chance per second
    "auto": 0.2,       # 20% chance per second
    "truck": 0.08,     # 8% chance per second
    "bus": 0.05        # 5% chance per second
}
```

## 🏗️ Code Architecture

```
project/
├── main.py           # Main simulation loop and UI
├── traffic_ai.py     # AI logic and dynamic timing
├── vehicle.py        # Vehicle class with physics
├── signal.py         # Traffic signal state management
├── utils.py          # Configuration and helper functions
└── README.md         # This documentation
```

### Key Classes
- **`FerokeTrafficSimulation`**: Main simulation controller
- **`TrafficAI`**: AI decision-making system
- **`TrafficSignal`**: Signal state management
- **`Vehicle`**: Individual vehicle behavior

## 🧪 Testing Scenarios

### Recommended Test Cases
1. **Balanced Traffic**: Equal vehicles on both sides
2. **Heavy Imbalance**: 10:1 ratio between sides
3. **No Traffic**: Verify minimum green times
4. **Starvation Test**: Block one side and observe priority boost
5. **Manual Override**: Test emergency control capabilities

### Performance Validation
- Maintain 60 FPS with 50+ vehicles
- Starvation prevention within 90 seconds
- Fair allocation under balanced conditions
- Responsive AI decisions (< 1 second)

## 🔮 Future Enhancements

### Real Camera Integration
Replace simulated vehicle detection with:
```python
def get_vehicle_counts_from_camera():
    # YOLO-based detection from real camera feeds
    # Returns: vehicle_counts_by_type
    pass
```

### Advanced Features
- **Emergency Vehicle Detection**: Siren recognition and priority override
- **Weather Adaptation**: Adjust timing for rain/fog conditions
- **Predictive Analytics**: Machine learning for traffic pattern prediction
- **Remote Monitoring**: Web dashboard for traffic management center
- **Mobile App**: Real-time bridge status for commuters

### Hardware Deployment
- **Edge Computing**: NVIDIA Jetson or Raspberry Pi
- **Cameras**: 1080p IP cameras with night vision
- **Connectivity**: 4G/5G backup for remote monitoring
- **Power**: Solar panels with battery backup

## 📈 Performance Metrics

The simulation tracks:
- **Throughput**: Vehicles per hour for each side
- **Efficiency**: Average wait time and queue length
- **Fairness**: Wait time variance between sides
- **Reliability**: System uptime and error rates

## 🤝 Contributing

This is a prototype for demonstration. For production deployment:
1. Conduct field testing with real traffic data
2. Integrate with local traffic management systems
3. Obtain necessary regulatory approvals
4. Implement redundancy and fail-safe mechanisms

## 📞 Contact

For questions about this simulation or real-world implementation:
- Technical discussions welcome
- Collaboration opportunities available
- Real deployment consulting possible

---

**Note**: This is a simulation prototype. Real-world deployment requires additional safety systems, regulatory approval, and extensive testing.
