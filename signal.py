"""
Traffic Signal class for the Feroke Bridge Traffic Simulation
Manages signal states, timing, and visual representation
"""

import pygame
import time
from utils import *

class TrafficSignal:
    """
    Manages the traffic signal system for the one-way bridge
    """
    
    # Signal states
    LEFT_GREEN = "LEFT_GREEN"
    LEFT_YELLOW = "LEFT_YELLOW"
    RIGHT_GREEN = "RIGHT_GREEN"
    RIGHT_YELLOW = "RIGHT_YELLOW"
    ALL_RED = "ALL_RED"
    
    def __init__(self):
        """Initialize the traffic signal system"""
        self.current_state = self.LEFT_GREEN
        self.state_start_time = time.time()
        self.state_duration = MIN_GREEN  # Initial duration

        # Timing for current cycle
        self.left_green_time = MIN_GREEN
        self.right_green_time = MIN_GREEN
        self.next_left_green_time = MIN_GREEN
        self.next_right_green_time = MIN_GREEN

        # State history for analysis
        self.state_history = []
        self.cycle_count = 0

        # Manual override flags
        self.manual_override = False
        self.override_state = None
        
    def update(self, left_green_duration, right_green_duration):
        """
        Update signal state based on AI-calculated durations

        Args:
            left_green_duration: Duration for left side green phase
            right_green_duration: Duration for right side green phase
        """
        # Store the new durations for next cycle
        self.next_left_green_time = left_green_duration
        self.next_right_green_time = right_green_duration

        current_time = time.time()
        elapsed_time = current_time - self.state_start_time

        # Check for manual override
        if self.manual_override:
            if self.override_state != self.current_state:
                self._change_state(self.override_state)
            return

        # State machine logic
        if elapsed_time >= self.state_duration:
            self._advance_to_next_state()
            
    def _advance_to_next_state(self):
        """Advance to the next state in the signal cycle"""
        if self.current_state == self.LEFT_GREEN:
            self._change_state(self.LEFT_YELLOW)
            self.state_duration = YELLOW_TIME

        elif self.current_state == self.LEFT_YELLOW:
            self._change_state(self.ALL_RED)
            self.state_duration = ALL_RED_TIME

        elif self.current_state == self.ALL_RED:
            if self._get_previous_state() == self.LEFT_YELLOW:
                # Transition to right green
                self._change_state(self.RIGHT_GREEN)
                self.state_duration = self.next_right_green_time
                self.right_green_time = self.next_right_green_time
            else:
                # Transition to left green (complete cycle)
                self._change_state(self.LEFT_GREEN)
                self.state_duration = self.next_left_green_time
                self.left_green_time = self.next_left_green_time
                self.cycle_count += 1

        elif self.current_state == self.RIGHT_GREEN:
            self._change_state(self.RIGHT_YELLOW)
            self.state_duration = YELLOW_TIME

        elif self.current_state == self.RIGHT_YELLOW:
            self._change_state(self.ALL_RED)
            self.state_duration = ALL_RED_TIME
            
    def _change_state(self, new_state):
        """Change to a new signal state"""
        # Record state change in history
        self.state_history.append({
            'from_state': self.current_state,
            'to_state': new_state,
            'timestamp': time.time(),
            'duration': time.time() - self.state_start_time
        })
        
        # Update state
        self.current_state = new_state
        self.state_start_time = time.time()
        
        # Keep history manageable
        if len(self.state_history) > 100:
            self.state_history = self.state_history[-50:]
            
    def _get_previous_state(self):
        """Get the previous state from history"""
        if len(self.state_history) > 0:
            return self.state_history[-1]['from_state']
        return self.LEFT_YELLOW  # Default assumption
        
    def get_remaining_time(self):
        """Get remaining time in current state"""
        elapsed = time.time() - self.state_start_time
        return max(0, self.state_duration - elapsed)
        
    def get_current_phase_info(self):
        """Get information about current signal phase"""
        return {
            'state': self.current_state,
            'remaining_time': self.get_remaining_time(),
            'total_duration': self.state_duration,
            'progress': min(1.0, (time.time() - self.state_start_time) / self.state_duration),
            'cycle_count': self.cycle_count
        }
        
    def set_manual_override(self, state):
        """Set manual override to specific state"""
        self.manual_override = True
        self.override_state = state
        
    def clear_manual_override(self):
        """Clear manual override and return to automatic operation"""
        self.manual_override = False
        self.override_state = None
        
    def is_green_for_side(self, side):
        """Check if signal is green for specified side"""
        if side == "LEFT":
            return self.current_state == self.LEFT_GREEN
        else:
            return self.current_state == self.RIGHT_GREEN
            
    def is_yellow_for_side(self, side):
        """Check if signal is yellow for specified side"""
        if side == "LEFT":
            return self.current_state == self.LEFT_YELLOW
        else:
            return self.current_state == self.RIGHT_YELLOW
            
    def render(self, surface, font):
        """
        Render traffic signals on the screen
        
        Args:
            surface: Pygame surface to draw on
            font: Font for text rendering
        """
        # Draw left signal
        self._draw_signal_light(surface, LEFT_SIGNAL_X, SIGNAL_Y, "LEFT", font)

        # Draw right signal
        self._draw_signal_light(surface, RIGHT_SIGNAL_X, SIGNAL_Y, "RIGHT", font)
        
        # Draw signal state information
        self._draw_signal_info(surface, font)
        
    def _draw_signal_light(self, surface, x, y, side, font):
        """Draw individual signal light"""
        # Signal housing (dark background)
        housing_rect = pygame.Rect(x - 20, y - 40, 40, 80)
        pygame.draw.rect(surface, (40, 40, 40), housing_rect, border_radius=5)
        pygame.draw.rect(surface, (60, 60, 60), housing_rect, width=2, border_radius=5)
        
        # Light positions
        red_pos = (x, y - 20)
        yellow_pos = (x, y)
        green_pos = (x, y + 20)
        
        # Determine which lights should be on
        red_on = self._should_light_be_on(side, 'red')
        yellow_on = self._should_light_be_on(side, 'yellow')
        green_on = self._should_light_be_on(side, 'green')
        
        # Draw lights
        pygame.draw.circle(surface, SIGNAL_RED if red_on else SIGNAL_OFF, red_pos, SIGNAL_RADIUS)
        pygame.draw.circle(surface, SIGNAL_YELLOW if yellow_on else SIGNAL_OFF, yellow_pos, SIGNAL_RADIUS)
        pygame.draw.circle(surface, SIGNAL_GREEN if green_on else SIGNAL_OFF, green_pos, SIGNAL_RADIUS)
        
        # Add light glow effect for active lights
        if red_on:
            pygame.draw.circle(surface, (*SIGNAL_RED, 100), red_pos, SIGNAL_RADIUS + 5)
        if yellow_on:
            pygame.draw.circle(surface, (*SIGNAL_YELLOW, 100), yellow_pos, SIGNAL_RADIUS + 5)
        if green_on:
            pygame.draw.circle(surface, (*SIGNAL_GREEN, 100), green_pos, SIGNAL_RADIUS + 5)
            
        # Draw side label
        label = "FEROKE" if side == "LEFT" else "RAMANATTUKARA"
        draw_text(surface, label, (x, y + 50), font, UI_TEXT, center=True)
        
    def _should_light_be_on(self, side, color):
        """Determine if a specific light should be on"""
        if side == "LEFT":
            if color == 'green':
                return self.current_state == self.LEFT_GREEN
            elif color == 'yellow':
                return self.current_state == self.LEFT_YELLOW
            elif color == 'red':
                return self.current_state in [self.RIGHT_GREEN, self.RIGHT_YELLOW, self.ALL_RED]
        else:  # RIGHT side
            if color == 'green':
                return self.current_state == self.RIGHT_GREEN
            elif color == 'yellow':
                return self.current_state == self.RIGHT_YELLOW
            elif color == 'red':
                return self.current_state in [self.LEFT_GREEN, self.LEFT_YELLOW, self.ALL_RED]
        return False
        
    def _draw_signal_info(self, surface, font):
        """Draw current signal state information"""
        info = self.get_current_phase_info()
        
        # Current state display
        state_text = f"Current: {info['state'].replace('_', ' ')}"
        draw_text(surface, state_text, (SCREEN_WIDTH // 2, 50), font, UI_ACCENT, center=True)
        
        # Remaining time
        time_text = f"Time Remaining: {info['remaining_time']:.1f}s"
        draw_text(surface, time_text, (SCREEN_WIDTH // 2, 80), font, UI_TEXT, center=True)
        
        # Cycle count
        cycle_text = f"Cycle: {info['cycle_count']}"
        draw_text(surface, cycle_text, (SCREEN_WIDTH // 2, 110), font, UI_TEXT, center=True)
        
        # Manual override indicator
        if self.manual_override:
            override_text = "MANUAL OVERRIDE ACTIVE"
            draw_text(surface, override_text, (SCREEN_WIDTH // 2, 140), font, UI_WARNING, center=True)
