"""
Demo launcher for the Feroke Bridge Traffic Simulation
Provides options to run different versions of the simulation
"""

import sys
import os

def print_banner():
    """Print the application banner"""
    print("=" * 70)
    print("🌉 SMART FEROKE BRIDGE TRAFFIC CONTROL SYSTEM 🌉")
    print("=" * 70)
    print("AI-Driven Dynamic Signal System for Old Feroke Bridge")
    print("Kozhikode, Kerala - Simulation Prototype")
    print("=" * 70)
    print()

def print_menu():
    """Print the main menu"""
    print("Choose simulation mode:")
    print()
    print("1. 🎮 Full GUI Simulation (Recommended)")
    print("   - Complete visual simulation with AI traffic control")
    print("   - Real-time statistics and interactive controls")
    print("   - Best for demonstration and testing")
    print()
    print("2. 📊 Headless Analysis Mode")
    print("   - Run simulation without graphics")
    print("   - Generate performance reports")
    print("   - Good for testing AI algorithms")
    print()
    print("3. 🧪 System Tests")
    print("   - Run comprehensive test suite")
    print("   - Verify all components work correctly")
    print("   - Check for errors and performance")
    print()
    print("4. ❓ Help & Information")
    print("   - View controls and features")
    print("   - Technical documentation")
    print("   - Project information")
    print()
    print("0. 🚪 Exit")
    print()

def run_gui_simulation():
    """Run the full GUI simulation"""
    print("Starting Full GUI Simulation...")
    print("=" * 50)
    print("CONTROLS:")
    print("  SPACE - Pause/Resume")
    print("  R - Reset simulation")
    print("  1 - Force left green (manual override)")
    print("  2 - Force right green (manual override)")
    print("  C - Clear manual override")
    print("  ESC - Exit")
    print("=" * 50)
    print()
    print("Loading simulation...")
    
    try:
        import main
        main.main()
    except ImportError as e:
        print(f"Error: Missing dependencies - {e}")
        print("Please install required packages: pip install pygame numpy")
    except Exception as e:
        print(f"Error running simulation: {e}")

def run_headless_analysis():
    """Run headless analysis"""
    print("Starting Headless Analysis Mode...")
    print("=" * 50)
    
    duration = input("Enter simulation duration in minutes (default: 3): ").strip()
    if not duration:
        duration = "3"
    
    try:
        duration_min = float(duration)
        if duration_min <= 0 or duration_min > 60:
            print("Invalid duration. Using default 3 minutes.")
            duration_min = 3
    except ValueError:
        print("Invalid input. Using default 3 minutes.")
        duration_min = 3
    
    print(f"Running {duration_min}-minute analysis...")
    print()
    
    try:
        # Modify the demo to use custom duration
        import demo_headless
        sim = demo_headless.HeadlessSimulation(duration_minutes=duration_min)
        sim.run()
    except ImportError as e:
        print(f"Error: Missing dependencies - {e}")
    except Exception as e:
        print(f"Error running analysis: {e}")

def run_system_tests():
    """Run system tests"""
    print("Running System Tests...")
    print("=" * 50)
    
    try:
        import test_simulation
        test_simulation.main()
    except ImportError as e:
        print(f"Error: Missing dependencies - {e}")
    except Exception as e:
        print(f"Error running tests: {e}")

def show_help():
    """Show help and information"""
    print("HELP & INFORMATION")
    print("=" * 50)
    print()
    print("🎯 PROJECT OVERVIEW:")
    print("This simulation demonstrates an AI-controlled traffic signal system")
    print("for the Old Feroke Bridge in Kozhikode, Kerala. The bridge is narrow")
    print("and allows only one-way traffic, making intelligent signal control")
    print("essential for optimal traffic flow.")
    print()
    print("🧠 AI FEATURES:")
    print("• Dynamic timing based on vehicle count and type")
    print("• Starvation prevention (no side waits too long)")
    print("• Fairness algorithms for balanced access")
    print("• Real-time adaptation to traffic conditions")
    print()
    print("🚗 VEHICLE TYPES & WEIGHTS:")
    print("• Bike: 0.5 PCU (fast, small)")
    print("• Car: 1.0 PCU (baseline)")
    print("• Auto: 1.2 PCU (medium)")
    print("• Truck: 2.0 PCU (large, slow)")
    print("• Bus: 2.5 PCU (largest, slowest)")
    print()
    print("⚙️ TECHNICAL SPECS:")
    print("• Python 3.10+ with Pygame")
    print("• 60 FPS real-time simulation")
    print("• Modular architecture for easy extension")
    print("• Ready for real camera integration")
    print()
    print("📁 FILES:")
    print("• main.py - Full GUI simulation")
    print("• demo_headless.py - Analysis mode")
    print("• test_simulation.py - Test suite")
    print("• README.md - Complete documentation")
    print()
    print("🔗 FUTURE INTEGRATION:")
    print("This prototype can be extended with:")
    print("• YOLO-based vehicle detection")
    print("• Real camera feeds")
    print("• IoT sensors and remote monitoring")
    print("• Emergency vehicle priority")
    print()
    input("Press Enter to continue...")

def check_dependencies():
    """Check if required dependencies are available"""
    try:
        import pygame
        import numpy
        return True
    except ImportError:
        return False

def main():
    """Main application entry point"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("⚠️  WARNING: Missing required dependencies!")
        print("Please install: pip install pygame numpy")
        print()
        choice = input("Continue anyway? (y/N): ").strip().lower()
        if choice != 'y':
            return
        print()
    
    while True:
        print_menu()
        choice = input("Enter your choice (0-4): ").strip()
        print()
        
        if choice == '1':
            run_gui_simulation()
        elif choice == '2':
            run_headless_analysis()
        elif choice == '3':
            run_system_tests()
        elif choice == '4':
            show_help()
        elif choice == '0':
            print("Thank you for using the Feroke Bridge Traffic Control System!")
            print("🌉 Drive safely! 🚗")
            break
        else:
            print("Invalid choice. Please enter 0-4.")
        
        print()
        input("Press Enter to return to main menu...")
        print("\n" * 2)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nSimulation interrupted by user.")
        print("Thank you for using the Feroke Bridge Traffic Control System!")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        print("Please check the installation and try again.")
