"""
Configuration constants and utility functions for the Feroke Bridge Traffic Simulation
"""

import pygame
import math

# ==================== SIMULATION CONSTANTS ====================

# Display settings
SCREEN_WIDTH = 1280
SCREEN_HEIGHT = 720
FPS = 60

# Traffic signal timing constants (in seconds)
MIN_GREEN = 5
MAX_GREEN = 40
YELLOW_TIME = 3
ALL_RED_TIME = 1
STARVATION_LIMIT = 60
BASE_CYCLE = 30

# Vehicle weights for AI calculations (Passenger Car Units - PCU)
VEHICLE_WEIGHTS = {
    "bike": 0.5,
    "car": 1.0,
    "auto": 1.2,
    "truck": 2.0,
    "bus": 2.5
}

# Vehicle properties
VEHICLE_SPEEDS = {
    "bike": 80,      # pixels per second
    "car": 60,
    "auto": 55,
    "truck": 40,
    "bus": 35
}

VEHICLE_SIZES = {
    "bike": (20, 12),
    "car": (35, 20),
    "auto": (25, 18),
    "truck": (50, 25),
    "bus": (55, 28)
}

# Vehicle spawn probabilities (per second)
SPAWN_PROBABILITIES = {
    "bike": 0.3,
    "car": 0.4,
    "auto": 0.2,
    "truck": 0.08,
    "bus": 0.05
}

# ==================== COLORS ====================

# Background colors
BACKGROUND_COLOR = (45, 55, 65)
ROAD_COLOR = (80, 80, 80)
BRIDGE_COLOR = (100, 100, 100)
WATER_COLOR = (30, 100, 150)
GRASS_COLOR = (60, 120, 60)

# Vehicle colors
VEHICLE_COLORS = {
    "bike": (50, 150, 255),    # Blue
    "car": (255, 80, 80),      # Red
    "auto": (255, 200, 50),    # Yellow
    "truck": (255, 140, 50),   # Orange
    "bus": (80, 200, 80)       # Green
}

# Signal colors
SIGNAL_RED = (255, 50, 50)
SIGNAL_YELLOW = (255, 200, 50)
SIGNAL_GREEN = (50, 255, 50)
SIGNAL_OFF = (80, 80, 80)

# UI colors
UI_BACKGROUND = (20, 25, 30)
UI_TEXT = (255, 255, 255)
UI_ACCENT = (100, 150, 200)
UI_WARNING = (255, 150, 50)
UI_SUCCESS = (50, 255, 100)

# ==================== LAYOUT CONSTANTS ====================

# Bridge layout
BRIDGE_X = SCREEN_WIDTH // 2 - 100
BRIDGE_WIDTH = 200
BRIDGE_Y = SCREEN_HEIGHT // 2 - 25
BRIDGE_HEIGHT = 50

# Signal positions
LEFT_SIGNAL_X = BRIDGE_X - 50
RIGHT_SIGNAL_X = BRIDGE_X + BRIDGE_WIDTH + 20
SIGNAL_Y = BRIDGE_Y + 10
SIGNAL_RADIUS = 15

# Vehicle spawn and movement areas
LEFT_SPAWN_X = 50
RIGHT_SPAWN_X = SCREEN_WIDTH - 50
VEHICLE_Y = BRIDGE_Y + BRIDGE_HEIGHT // 2

# Stop lines (where vehicles wait for green signal)
LEFT_STOP_LINE = BRIDGE_X - 30
RIGHT_STOP_LINE = BRIDGE_X + BRIDGE_WIDTH + 10

# Detection zones for AI camera simulation
LEFT_DETECTION_ZONE = (50, BRIDGE_X - 100)
RIGHT_DETECTION_ZONE = (BRIDGE_X + BRIDGE_WIDTH + 100, SCREEN_WIDTH - 50)

# UI panel dimensions
UI_PANEL_HEIGHT = 150
UI_PANEL_Y = SCREEN_HEIGHT - UI_PANEL_HEIGHT

# ==================== UTILITY FUNCTIONS ====================

def clamp(value, min_val, max_val):
    """Clamp a value between min and max bounds"""
    return max(min_val, min(max_val, value))

def lerp(start, end, t):
    """Linear interpolation between start and end by factor t (0-1)"""
    return start + (end - start) * t

def distance(pos1, pos2):
    """Calculate Euclidean distance between two points"""
    return math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

def draw_text(surface, text, pos, font, color=UI_TEXT, center=False):
    """Draw text on surface with optional centering"""
    text_surface = font.render(str(text), True, color)
    if center:
        rect = text_surface.get_rect(center=pos)
        surface.blit(text_surface, rect)
    else:
        surface.blit(text_surface, pos)

def draw_rounded_rect(surface, color, rect, radius=5):
    """Draw a rounded rectangle"""
    pygame.draw.rect(surface, color, rect, border_radius=radius)

def format_time(seconds):
    """Format seconds as MM:SS"""
    minutes = int(seconds // 60)
    seconds = int(seconds % 60)
    return f"{minutes:02d}:{seconds:02d}"

def get_vehicle_color_with_alpha(vehicle_type, alpha=255):
    """Get vehicle color with specified alpha for transparency effects"""
    color = VEHICLE_COLORS[vehicle_type]
    return (*color, alpha)

# ==================== PHYSICS HELPERS ====================

def apply_acceleration(current_velocity, target_velocity, acceleration, dt):
    """Apply acceleration towards target velocity"""
    if abs(target_velocity - current_velocity) < acceleration * dt:
        return target_velocity
    elif target_velocity > current_velocity:
        return current_velocity + acceleration * dt
    else:
        return current_velocity - acceleration * dt

def check_collision_ahead(vehicle_pos, vehicle_size, other_vehicles, direction, safety_distance=10):
    """Check if there's a vehicle ahead that would cause collision"""
    vehicle_front = vehicle_pos[0] + (vehicle_size[0] if direction > 0 else 0)
    
    for other in other_vehicles:
        if other['pos'][1] != vehicle_pos[1]:  # Different lane
            continue
            
        other_back = other['pos'][0] + (0 if direction > 0 else other['size'][0])
        
        if direction > 0:  # Moving right
            if other_back > vehicle_front and other_back - vehicle_front < safety_distance + other['size'][0]:
                return True
        else:  # Moving left
            if other_back < vehicle_front and vehicle_front - other_back < safety_distance + other['size'][0]:
                return True
    
    return False
