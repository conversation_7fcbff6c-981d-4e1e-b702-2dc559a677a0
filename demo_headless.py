"""
Headless demo of the Feroke Bridge Traffic Simulation
Runs the simulation logic without GUI for testing and analysis
"""

import time
import random
from vehicle import Vehicle
from signal import TrafficSignal
from traffic_ai import TrafficAI
from utils import *

class HeadlessSimulation:
    """Headless version of the traffic simulation for testing"""
    
    def __init__(self, duration_minutes=5):
        """Initialize headless simulation"""
        self.duration = duration_minutes * 60  # Convert to seconds
        self.traffic_signal = TrafficSignal()
        self.traffic_ai = TrafficAI()
        
        self.left_vehicles = []
        self.right_vehicles = []
        self.simulation_time = 0
        self.last_spawn_time = {"LEFT": 0, "RIGHT": 0}
        
        # Statistics
        self.stats = {
            'vehicles_spawned_left': 0,
            'vehicles_spawned_right': 0,
            'vehicles_completed_left': 0,
            'vehicles_completed_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'max_wait_time_left': 0,
            'max_wait_time_right': 0,
            'signal_changes': 0,
            'starvation_events': 0
        }
        
        self.last_signal_state = self.traffic_signal.current_state
        
    def spawn_vehicles(self, dt):
        """Spawn vehicles based on probabilities"""
        current_time = self.simulation_time
        
        # Spawn on left side
        if current_time - self.last_spawn_time["LEFT"] > 1.0:
            for vehicle_type, probability in SPAWN_PROBABILITIES.items():
                if random.random() < probability * dt:
                    vehicle = Vehicle(vehicle_type, "LEFT", LEFT_SPAWN_X)
                    self.left_vehicles.append(vehicle)
                    self.stats['vehicles_spawned_left'] += 1
                    self.last_spawn_time["LEFT"] = current_time
                    break
                    
        # Spawn on right side
        if current_time - self.last_spawn_time["RIGHT"] > 1.0:
            for vehicle_type, probability in SPAWN_PROBABILITIES.items():
                if random.random() < probability * dt:
                    vehicle = Vehicle(vehicle_type, "RIGHT", RIGHT_SPAWN_X)
                    self.right_vehicles.append(vehicle)
                    self.stats['vehicles_spawned_right'] += 1
                    self.last_spawn_time["RIGHT"] = current_time
                    break
    
    def update_vehicles(self, dt):
        """Update all vehicles"""
        for vehicle in self.left_vehicles:
            vehicle.update(dt, self.traffic_signal.current_state, self.left_vehicles)
            
        for vehicle in self.right_vehicles:
            vehicle.update(dt, self.traffic_signal.current_state, self.right_vehicles)
    
    def cleanup_vehicles(self):
        """Remove completed vehicles"""
        # Left side
        for vehicle in self.left_vehicles[:]:
            if vehicle.should_be_removed():
                self.left_vehicles.remove(vehicle)
                self.stats['vehicles_completed_left'] += 1
                self.stats['total_wait_time_left'] += vehicle.total_wait_time
                self.stats['max_wait_time_left'] = max(
                    self.stats['max_wait_time_left'], vehicle.total_wait_time
                )
        
        # Right side
        for vehicle in self.right_vehicles[:]:
            if vehicle.should_be_removed():
                self.right_vehicles.remove(vehicle)
                self.stats['vehicles_completed_right'] += 1
                self.stats['total_wait_time_right'] += vehicle.total_wait_time
                self.stats['max_wait_time_right'] = max(
                    self.stats['max_wait_time_right'], vehicle.total_wait_time
                )
    
    def run(self):
        """Run the headless simulation"""
        print(f"Starting {self.duration/60:.1f}-minute headless simulation...")
        print("=" * 60)
        
        dt = 1.0  # 1 second time steps for better signal timing
        last_report_time = 0
        report_interval = 15  # Report every 15 seconds
        
        while self.simulation_time < self.duration:
            # Update simulation
            self.spawn_vehicles(dt)
            
            # Update AI
            self.traffic_ai.update_waiting_times(self.traffic_signal.current_state, dt)
            left_green, right_green = self.traffic_ai.calculate_green_times(
                self.left_vehicles, self.right_vehicles
            )
            
            # Update signal
            self.traffic_signal.update(left_green, right_green)
            
            # Track signal changes
            if self.traffic_signal.current_state != self.last_signal_state:
                self.stats['signal_changes'] += 1
                self.last_signal_state = self.traffic_signal.current_state
            
            # Update vehicles
            self.update_vehicles(dt)
            self.cleanup_vehicles()
            
            # Progress reporting
            if self.simulation_time - last_report_time >= report_interval:
                self.print_progress_report()
                last_report_time = self.simulation_time
            
            self.simulation_time += dt

            # Add small delay to make timing more realistic
            time.sleep(0.1)
        
        self.print_final_report()
    
    def print_progress_report(self):
        """Print progress report"""
        elapsed_min = self.simulation_time / 60
        left_queue = len([v for v in self.left_vehicles if v.is_in_detection_zone()])
        right_queue = len([v for v in self.right_vehicles if v.is_in_detection_zone()])
        
        ai_status = self.traffic_ai.get_ai_status()
        
        print(f"Time: {elapsed_min:.1f}min | "
              f"Signal: {self.traffic_signal.current_state.replace('_', ' ')} | "
              f"Queues: L={left_queue} R={right_queue} | "
              f"Wait: L={ai_status['left_wait_time']:.1f}s R={ai_status['right_wait_time']:.1f}s")
    
    def print_final_report(self):
        """Print comprehensive final report"""
        print("\n" + "=" * 60)
        print("SIMULATION COMPLETED - FINAL REPORT")
        print("=" * 60)
        
        # Basic statistics
        print(f"Duration: {self.duration/60:.1f} minutes")
        print(f"Total Signal Changes: {self.stats['signal_changes']}")
        print()
        
        # Vehicle statistics
        print("VEHICLE STATISTICS:")
        print(f"  Feroke Side (Left):")
        print(f"    Spawned: {self.stats['vehicles_spawned_left']}")
        print(f"    Completed: {self.stats['vehicles_completed_left']}")
        print(f"    Completion Rate: {self.get_completion_rate('left'):.1f}%")
        print()
        
        print(f"  Ramanattukara Side (Right):")
        print(f"    Spawned: {self.stats['vehicles_spawned_right']}")
        print(f"    Completed: {self.stats['vehicles_completed_right']}")
        print(f"    Completion Rate: {self.get_completion_rate('right'):.1f}%")
        print()
        
        # Wait time analysis
        print("WAIT TIME ANALYSIS:")
        avg_wait_left = self.get_average_wait_time('left')
        avg_wait_right = self.get_average_wait_time('right')
        
        print(f"  Feroke Side:")
        print(f"    Average Wait: {avg_wait_left:.1f} seconds")
        print(f"    Maximum Wait: {self.stats['max_wait_time_left']:.1f} seconds")
        print()
        
        print(f"  Ramanattukara Side:")
        print(f"    Average Wait: {avg_wait_right:.1f} seconds")
        print(f"    Maximum Wait: {self.stats['max_wait_time_right']:.1f} seconds")
        print()
        
        # Performance metrics
        print("PERFORMANCE METRICS:")
        total_vehicles = (self.stats['vehicles_completed_left'] + 
                         self.stats['vehicles_completed_right'])
        throughput = total_vehicles / (self.duration / 3600)  # vehicles per hour
        
        print(f"  Total Throughput: {throughput:.1f} vehicles/hour")
        print(f"  Average Wait Time: {(avg_wait_left + avg_wait_right) / 2:.1f} seconds")
        
        # Fairness analysis
        fairness_ratio = min(avg_wait_left, avg_wait_right) / max(avg_wait_left, avg_wait_right) if max(avg_wait_left, avg_wait_right) > 0 else 1.0
        print(f"  Fairness Index: {fairness_ratio:.2f} (1.0 = perfectly fair)")
        
        # AI performance
        ai_status = self.traffic_ai.get_ai_status()
        print(f"  Starvation Events: {ai_status['performance_metrics']['starvation_events']}")
        print()
        
        # Recommendations
        print("RECOMMENDATIONS:")
        if avg_wait_left > 45 or avg_wait_right > 45:
            print("  ⚠️  High wait times detected - consider adjusting timing parameters")
        if fairness_ratio < 0.7:
            print("  ⚠️  Unfair allocation detected - review AI fairness algorithm")
        if self.stats['max_wait_time_left'] > STARVATION_LIMIT or self.stats['max_wait_time_right'] > STARVATION_LIMIT:
            print("  ⚠️  Starvation occurred - strengthen prevention mechanisms")
        
        if (avg_wait_left < 30 and avg_wait_right < 30 and 
            fairness_ratio > 0.8 and 
            self.stats['max_wait_time_left'] < STARVATION_LIMIT and 
            self.stats['max_wait_time_right'] < STARVATION_LIMIT):
            print("  ✅ Excellent performance - system working optimally!")
        
        print("=" * 60)
    
    def get_completion_rate(self, side):
        """Calculate completion rate for a side"""
        if side == 'left':
            spawned = self.stats['vehicles_spawned_left']
            completed = self.stats['vehicles_completed_left']
        else:
            spawned = self.stats['vehicles_spawned_right']
            completed = self.stats['vehicles_completed_right']
        
        return (completed / spawned * 100) if spawned > 0 else 0
    
    def get_average_wait_time(self, side):
        """Calculate average wait time for a side"""
        if side == 'left':
            total_wait = self.stats['total_wait_time_left']
            completed = self.stats['vehicles_completed_left']
        else:
            total_wait = self.stats['total_wait_time_right']
            completed = self.stats['vehicles_completed_right']
        
        return total_wait / completed if completed > 0 else 0


def main():
    """Run headless simulation demo"""
    print("Feroke Bridge Traffic Simulation - Headless Demo")
    print("This demo runs the AI logic without graphics for testing")
    print()
    
    # Run simulation
    sim = HeadlessSimulation(duration_minutes=2)  # 2-minute demo
    sim.run()


if __name__ == "__main__":
    main()
