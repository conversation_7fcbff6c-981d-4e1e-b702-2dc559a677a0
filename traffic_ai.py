"""
AI Traffic Control System for the Feroke Bridge
Implements dynamic timing algorithms with starvation prevention
"""

import time
import math
from utils import *

class TrafficAI:
    """
    Enhanced AI system with periodic decision-making and intelligent traffic analysis
    """

    def __init__(self):
        """Initialize the AI traffic control system"""
        # Waiting time tracking for starvation prevention
        self.left_wait_time = 0
        self.right_wait_time = 0

        # Periodic decision-making
        self.decision_interval = 8.0  # Make decisions every 8 seconds
        self.last_decision_time = time.time()
        self.current_decision = None
        self.decision_reason = []

        # Enhanced starvation prevention
        self.max_wait_time = 90  # Maximum wait time before emergency priority
        self.starvation_boost = 15  # Extra seconds for starved side

        # Historical data for analysis
        self.decision_history = []
        self.traffic_patterns = {
            'left_peak_times': [],
            'right_peak_times': [],
            'balanced_periods': []
        }

        self.performance_metrics = {
            'total_vehicles_left': 0,
            'total_vehicles_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'cycles_completed': 0,
            'starvation_events': 0,
            'emergency_interventions': 0,
            'decisions_made': 0
        }

        # AI parameters (tunable)
        self.aging_factor = 0.6
        self.fairness_weight = 0.4
        self.efficiency_weight = 0.6
        self.emergency_threshold = 0.8  # When to trigger emergency intervention
        
    def update_waiting_times(self, signal_state, dt):
        """
        Update waiting times for starvation prevention
        
        Args:
            signal_state: Current traffic signal state
            dt: Delta time in seconds
        """
        if signal_state in ["RIGHT_GREEN", "RIGHT_YELLOW", "ALL_RED"]:
            self.left_wait_time += dt
        else:
            self.left_wait_time = max(0, self.left_wait_time - dt * 0.5)
            
        if signal_state in ["LEFT_GREEN", "LEFT_YELLOW", "ALL_RED"]:
            self.right_wait_time += dt
        else:
            self.right_wait_time = max(0, self.right_wait_time - dt * 0.5)
            
    def should_make_decision(self):
        """Check if it's time to make a new AI decision"""
        current_time = time.time()
        return (current_time - self.last_decision_time) >= self.decision_interval

    def make_intelligent_decision(self, left_vehicles, right_vehicles, current_signal_state):
        """
        Make an intelligent traffic decision with enhanced analysis

        Args:
            left_vehicles: List of vehicles on left side
            right_vehicles: List of vehicles on right side
            current_signal_state: Current traffic signal state

        Returns:
            dict: Decision with timing, reasoning, and priority
        """
        if not self.should_make_decision():
            return self.current_decision

        # Reset decision reasoning
        self.decision_reason = []

        # Analyze current traffic situation
        analysis = self._analyze_traffic_situation(left_vehicles, right_vehicles)

        # Calculate optimal green times with enhanced logic
        left_green, right_green = self._calculate_enhanced_green_times(analysis)

        # Determine priority and urgency
        priority_side, urgency_level = self._determine_priority(analysis)

        # Create decision object
        decision = {
            'left_green_time': left_green,
            'right_green_time': right_green,
            'priority_side': priority_side,
            'urgency_level': urgency_level,
            'reasoning': self.decision_reason.copy(),
            'timestamp': time.time(),
            'analysis': analysis
        }

        # Update decision tracking
        self.current_decision = decision
        self.last_decision_time = time.time()
        self.performance_metrics['decisions_made'] += 1

        # Record decision for analysis
        self._record_enhanced_decision(decision, left_vehicles, right_vehicles)

        return decision

    def _analyze_traffic_situation(self, left_vehicles, right_vehicles):
        """Comprehensive traffic situation analysis"""
        analysis = {
            'left_count': self._calculate_weighted_count(left_vehicles),
            'right_count': self._calculate_weighted_count(right_vehicles),
            'left_queue': self._calculate_queue_length(left_vehicles),
            'right_queue': self._calculate_queue_length(right_vehicles),
            'left_waiting_vehicles': self._count_waiting_vehicles(left_vehicles),
            'right_waiting_vehicles': self._count_waiting_vehicles(right_vehicles),
            'left_heavy_vehicles': self._count_heavy_vehicles(left_vehicles),
            'right_heavy_vehicles': self._count_heavy_vehicles(right_vehicles),
            'traffic_imbalance': 0,
            'starvation_risk': 'none'
        }

        # Calculate traffic imbalance
        total_traffic = analysis['left_count'] + analysis['right_count']
        if total_traffic > 0:
            left_ratio = analysis['left_count'] / total_traffic
            analysis['traffic_imbalance'] = abs(left_ratio - 0.5)  # 0 = balanced, 0.5 = completely imbalanced

        # Assess starvation risk
        if self.left_wait_time > self.max_wait_time * 0.8 or self.right_wait_time > self.max_wait_time * 0.8:
            analysis['starvation_risk'] = 'high'
        elif self.left_wait_time > STARVATION_LIMIT or self.right_wait_time > STARVATION_LIMIT:
            analysis['starvation_risk'] = 'medium'
        else:
            analysis['starvation_risk'] = 'low'

        return analysis

    def _calculate_enhanced_green_times(self, analysis):
        """Calculate green times using enhanced AI logic"""
        left_count = analysis['left_count']
        right_count = analysis['right_count']
        total_demand = left_count + right_count

        if total_demand == 0:
            self.decision_reason.append("No traffic detected - using minimum green times")
            return MIN_GREEN, MIN_GREEN

        # Base proportional allocation
        base_cycle = BASE_CYCLE
        available_green = base_cycle - 2 * YELLOW_TIME - 2 * ALL_RED_TIME

        left_proportion = left_count / total_demand
        right_proportion = right_count / total_demand

        left_green = left_proportion * available_green
        right_green = right_proportion * available_green

        # Apply intelligent adjustments
        left_green, right_green = self._apply_intelligent_adjustments(
            left_green, right_green, analysis
        )

        # Apply enhanced starvation prevention
        left_green, right_green = self._apply_enhanced_starvation_prevention(
            left_green, right_green, analysis
        )

        # Clamp to valid ranges
        left_green = clamp(left_green, MIN_GREEN, MAX_GREEN)
        right_green = clamp(right_green, MIN_GREEN, MAX_GREEN)

        return int(left_green), int(right_green)

    def _apply_intelligent_adjustments(self, left_green, right_green, analysis):
        """Apply intelligent adjustments based on traffic analysis"""

        # Heavy vehicle adjustment
        if analysis['left_heavy_vehicles'] > analysis['right_heavy_vehicles'] + 1:
            left_green += 5
            self.decision_reason.append(f"Added 5s to left for {analysis['left_heavy_vehicles']} heavy vehicles")
        elif analysis['right_heavy_vehicles'] > analysis['left_heavy_vehicles'] + 1:
            right_green += 5
            self.decision_reason.append(f"Added 5s to right for {analysis['right_heavy_vehicles']} heavy vehicles")

        # Queue length adjustment
        queue_diff = analysis['left_queue'] - analysis['right_queue']
        if abs(queue_diff) > 3:
            if queue_diff > 0:
                left_green += min(8, queue_diff * 2)
                self.decision_reason.append(f"Added {min(8, queue_diff * 2)}s to left for longer queue")
            else:
                right_green += min(8, abs(queue_diff) * 2)
                self.decision_reason.append(f"Added {min(8, abs(queue_diff) * 2)}s to right for longer queue")

        # Traffic imbalance adjustment
        if analysis['traffic_imbalance'] > 0.3:  # Significant imbalance
            if analysis['left_count'] > analysis['right_count']:
                left_green += 6
                self.decision_reason.append("Added 6s to left due to traffic imbalance")
            else:
                right_green += 6
                self.decision_reason.append("Added 6s to right due to traffic imbalance")

        return left_green, right_green

    def _apply_enhanced_starvation_prevention(self, left_green, right_green, analysis):
        """Enhanced starvation prevention with emergency intervention"""

        # Emergency intervention for extreme wait times
        if self.left_wait_time > self.max_wait_time:
            left_green = MAX_GREEN
            right_green = MIN_GREEN
            self.decision_reason.append(f"EMERGENCY: Left side waited {self.left_wait_time:.1f}s - maximum green granted")
            self.performance_metrics['emergency_interventions'] += 1
            return left_green, right_green

        if self.right_wait_time > self.max_wait_time:
            right_green = MAX_GREEN
            left_green = MIN_GREEN
            self.decision_reason.append(f"EMERGENCY: Right side waited {self.right_wait_time:.1f}s - maximum green granted")
            self.performance_metrics['emergency_interventions'] += 1
            return left_green, right_green

        # Standard starvation prevention
        if self.left_wait_time > STARVATION_LIMIT:
            left_green += self.starvation_boost
            self.decision_reason.append(f"Added {self.starvation_boost}s to left for starvation prevention")
            self.performance_metrics['starvation_events'] += 1

        if self.right_wait_time > STARVATION_LIMIT:
            right_green += self.starvation_boost
            self.decision_reason.append(f"Added {self.starvation_boost}s to right for starvation prevention")
            self.performance_metrics['starvation_events'] += 1

        # Aging factor application
        aging_left = self.aging_factor * (self.left_wait_time / STARVATION_LIMIT)
        aging_right = self.aging_factor * (self.right_wait_time / STARVATION_LIMIT)

        if aging_left > 0.5:
            left_green += aging_left * 3
            self.decision_reason.append(f"Added {aging_left * 3:.1f}s to left for aging factor")

        if aging_right > 0.5:
            right_green += aging_right * 3
            self.decision_reason.append(f"Added {aging_right * 3:.1f}s to right for aging factor")

        return left_green, right_green

    def _determine_priority(self, analysis):
        """Determine which side has priority and urgency level"""
        urgency_level = 'normal'

        # Emergency priority
        if self.left_wait_time > self.max_wait_time:
            return 'left', 'emergency'
        if self.right_wait_time > self.max_wait_time:
            return 'right', 'emergency'

        # High priority for starvation risk
        if analysis['starvation_risk'] == 'high':
            urgency_level = 'high'
            if self.left_wait_time > self.right_wait_time:
                return 'left', urgency_level
            else:
                return 'right', urgency_level

        # Medium priority for imbalanced traffic
        if analysis['traffic_imbalance'] > 0.4:
            urgency_level = 'medium'
            if analysis['left_count'] > analysis['right_count']:
                return 'left', urgency_level
            else:
                return 'right', urgency_level

        # Normal priority - balanced approach
        if analysis['left_count'] >= analysis['right_count']:
            return 'left', 'normal'
        else:
            return 'right', 'normal'

    def _count_waiting_vehicles(self, vehicles):
        """Count vehicles that are currently waiting (not moving)"""
        return len([v for v in vehicles if v.is_in_detection_zone() and not v.is_moving])

    def _count_heavy_vehicles(self, vehicles):
        """Count heavy vehicles (trucks and buses)"""
        return len([v for v in vehicles if v.is_in_detection_zone() and v.type in ['truck', 'bus']])

    def _calculate_weighted_count(self, vehicles):
        """Calculate weighted vehicle count (PCU - Passenger Car Units)"""
        total_weight = 0
        for vehicle in vehicles:
            if vehicle.is_in_detection_zone():
                total_weight += vehicle.weight
        return total_weight
        
    def _calculate_queue_length(self, vehicles):
        """Calculate number of vehicles waiting in queue"""
        queue_count = 0
        for vehicle in vehicles:
            if vehicle.is_in_detection_zone() and not vehicle.is_moving:
                queue_count += 1
        return queue_count
        
    def _apply_starvation_prevention(self, left_green, right_green, left_count, right_count):
        """Apply starvation prevention logic"""
        starvation_boost = 10  # Extra seconds for starved side
        
        # Check for starvation conditions
        left_starved = self.left_wait_time > STARVATION_LIMIT
        right_starved = self.right_wait_time > STARVATION_LIMIT
        
        if left_starved and not right_starved:
            left_green += starvation_boost
            self.performance_metrics['starvation_events'] += 1
        elif right_starved and not left_starved:
            right_green += starvation_boost
            self.performance_metrics['starvation_events'] += 1
        elif left_starved and right_starved:
            # Both sides starved - prioritize by wait time
            if self.left_wait_time > self.right_wait_time:
                left_green += starvation_boost
            else:
                right_green += starvation_boost
                
        # Apply aging factor for gradual priority increase
        aging_left = self.aging_factor * (self.left_wait_time / STARVATION_LIMIT)
        aging_right = self.aging_factor * (self.right_wait_time / STARVATION_LIMIT)
        
        left_green += aging_left * 5
        right_green += aging_right * 5
        
        return left_green, right_green
        
    def _apply_fairness_adjustment(self, left_green, right_green, left_queue, right_queue):
        """Apply fairness adjustments based on queue lengths"""
        if left_queue == 0 and right_queue == 0:
            return left_green, right_green
            
        # Adjust based on queue imbalance
        total_queue = left_queue + right_queue
        if total_queue > 0:
            queue_ratio_left = left_queue / total_queue
            queue_ratio_right = right_queue / total_queue
            
            # Boost green time for side with longer queue
            if queue_ratio_left > 0.7:  # Left side has much longer queue
                left_green += 5
            elif queue_ratio_right > 0.7:  # Right side has much longer queue
                right_green += 5
                
        return left_green, right_green
        
    def _record_enhanced_decision(self, decision, left_vehicles, right_vehicles):
        """Record enhanced AI decision for analysis and learning"""
        enhanced_record = {
            'timestamp': decision['timestamp'],
            'left_count': len([v for v in left_vehicles if v.is_in_detection_zone()]),
            'right_count': len([v for v in right_vehicles if v.is_in_detection_zone()]),
            'left_weighted': decision['analysis']['left_count'],
            'right_weighted': decision['analysis']['right_count'],
            'left_wait_time': self.left_wait_time,
            'right_wait_time': self.right_wait_time,
            'left_green_assigned': decision['left_green_time'],
            'right_green_assigned': decision['right_green_time'],
            'priority_side': decision['priority_side'],
            'urgency_level': decision['urgency_level'],
            'reasoning': decision['reasoning'],
            'traffic_imbalance': decision['analysis']['traffic_imbalance'],
            'starvation_risk': decision['analysis']['starvation_risk'],
            'emergency_intervention': decision['urgency_level'] == 'emergency'
        }

        self.decision_history.append(enhanced_record)

        # Keep history manageable
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-50:]

    def add_manual_vehicle(self, side, vehicle_type):
        """Add a manual vehicle for testing purposes"""
        self.decision_reason.append(f"Manual {vehicle_type} added to {side} side")
        # Force immediate decision recalculation
        self.last_decision_time = 0
            
    def get_ai_status(self):
        """Get current AI system status and metrics"""
        return {
            'left_wait_time': self.left_wait_time,
            'right_wait_time': self.right_wait_time,
            'starvation_risk_left': self.left_wait_time / STARVATION_LIMIT,
            'starvation_risk_right': self.right_wait_time / STARVATION_LIMIT,
            'decisions_made': len(self.decision_history),
            'performance_metrics': self.performance_metrics.copy()
        }
        
    def get_recommendation_reason(self, left_vehicles, right_vehicles):
        """Get human-readable explanation of AI decision"""
        left_count = self._calculate_weighted_count(left_vehicles)
        right_count = self._calculate_weighted_count(right_vehicles)
        left_queue = self._calculate_queue_length(left_vehicles)
        right_queue = self._calculate_queue_length(right_vehicles)
        
        reasons = []
        
        # Traffic demand analysis
        if left_count > right_count * 1.5:
            reasons.append(f"Heavy traffic on Feroke side ({left_count:.1f} PCU vs {right_count:.1f} PCU)")
        elif right_count > left_count * 1.5:
            reasons.append(f"Heavy traffic on Ramanattukara side ({right_count:.1f} PCU vs {left_count:.1f} PCU)")
        else:
            reasons.append(f"Balanced traffic (L:{left_count:.1f}, R:{right_count:.1f} PCU)")
            
        # Queue analysis
        if left_queue > right_queue + 2:
            reasons.append(f"Long queue on Feroke side ({left_queue} vehicles)")
        elif right_queue > left_queue + 2:
            reasons.append(f"Long queue on Ramanattukara side ({right_queue} vehicles)")
            
        # Starvation analysis
        if self.left_wait_time > STARVATION_LIMIT * 0.8:
            reasons.append(f"Feroke side approaching starvation ({self.left_wait_time:.1f}s wait)")
        if self.right_wait_time > STARVATION_LIMIT * 0.8:
            reasons.append(f"Ramanattukara side approaching starvation ({self.right_wait_time:.1f}s wait)")
            
        return reasons if reasons else ["Normal traffic conditions"]
        
    def update_performance_metrics(self, vehicles_passed_left, vehicles_passed_right, 
                                 avg_wait_left, avg_wait_right):
        """Update performance metrics for analysis"""
        self.performance_metrics['total_vehicles_left'] += vehicles_passed_left
        self.performance_metrics['total_vehicles_right'] += vehicles_passed_right
        self.performance_metrics['total_wait_time_left'] += avg_wait_left
        self.performance_metrics['total_wait_time_right'] += avg_wait_right
        
    def reset_metrics(self):
        """Reset all performance metrics"""
        self.performance_metrics = {
            'total_vehicles_left': 0,
            'total_vehicles_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'cycles_completed': 0,
            'starvation_events': 0
        }
        self.left_wait_time = 0
        self.right_wait_time = 0
        self.decision_history.clear()
