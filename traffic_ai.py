"""
AI Traffic Control System for the Feroke Bridge
Implements dynamic timing algorithms with starvation prevention
"""

import time
import math
from utils import *

class TrafficAI:
    """
    AI system that calculates optimal signal timing based on traffic conditions
    """
    
    def __init__(self):
        """Initialize the AI traffic control system"""
        # Waiting time tracking for starvation prevention
        self.left_wait_time = 0
        self.right_wait_time = 0
        
        # Historical data for analysis
        self.decision_history = []
        self.performance_metrics = {
            'total_vehicles_left': 0,
            'total_vehicles_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'cycles_completed': 0,
            'starvation_events': 0
        }
        
        # AI parameters (tunable)
        self.aging_factor = 0.5
        self.fairness_weight = 0.3
        self.efficiency_weight = 0.7
        
        # Last decision timestamp
        self.last_decision_time = time.time()
        
    def update_waiting_times(self, signal_state, dt):
        """
        Update waiting times for starvation prevention
        
        Args:
            signal_state: Current traffic signal state
            dt: Delta time in seconds
        """
        if signal_state in ["RIGHT_GREEN", "RIGHT_YELLOW", "ALL_RED"]:
            self.left_wait_time += dt
        else:
            self.left_wait_time = max(0, self.left_wait_time - dt * 0.5)
            
        if signal_state in ["LEFT_GREEN", "LEFT_YELLOW", "ALL_RED"]:
            self.right_wait_time += dt
        else:
            self.right_wait_time = max(0, self.right_wait_time - dt * 0.5)
            
    def calculate_green_times(self, left_vehicles, right_vehicles):
        """
        Calculate optimal green times for both sides using AI algorithm
        
        Args:
            left_vehicles: List of vehicles on left side
            right_vehicles: List of vehicles on right side
            
        Returns:
            tuple: (left_green_time, right_green_time)
        """
        # Calculate weighted vehicle counts
        left_count = self._calculate_weighted_count(left_vehicles)
        right_count = self._calculate_weighted_count(right_vehicles)
        
        # Calculate queue lengths (vehicles in detection zone)
        left_queue = self._calculate_queue_length(left_vehicles)
        right_queue = self._calculate_queue_length(right_vehicles)
        
        # Base calculation using proportional allocation
        total_demand = left_count + right_count
        
        if total_demand == 0:
            # No traffic - use minimum green times
            left_green = MIN_GREEN
            right_green = MIN_GREEN
        else:
            # Proportional allocation based on demand
            base_cycle = BASE_CYCLE
            available_green = base_cycle - 2 * YELLOW_TIME - 2 * ALL_RED_TIME
            
            left_proportion = left_count / total_demand
            right_proportion = right_count / total_demand
            
            left_green = left_proportion * available_green
            right_green = right_proportion * available_green
        
        # Apply starvation prevention
        left_green, right_green = self._apply_starvation_prevention(
            left_green, right_green, left_count, right_count
        )
        
        # Apply fairness adjustments
        left_green, right_green = self._apply_fairness_adjustment(
            left_green, right_green, left_queue, right_queue
        )
        
        # Clamp to valid ranges
        left_green = clamp(left_green, MIN_GREEN, MAX_GREEN)
        right_green = clamp(right_green, MIN_GREEN, MAX_GREEN)
        
        # Record decision for analysis
        self._record_decision(left_vehicles, right_vehicles, left_green, right_green)
        
        return int(left_green), int(right_green)
        
    def _calculate_weighted_count(self, vehicles):
        """Calculate weighted vehicle count (PCU - Passenger Car Units)"""
        total_weight = 0
        for vehicle in vehicles:
            if vehicle.is_in_detection_zone():
                total_weight += vehicle.weight
        return total_weight
        
    def _calculate_queue_length(self, vehicles):
        """Calculate number of vehicles waiting in queue"""
        queue_count = 0
        for vehicle in vehicles:
            if vehicle.is_in_detection_zone() and not vehicle.is_moving:
                queue_count += 1
        return queue_count
        
    def _apply_starvation_prevention(self, left_green, right_green, left_count, right_count):
        """Apply starvation prevention logic"""
        starvation_boost = 10  # Extra seconds for starved side
        
        # Check for starvation conditions
        left_starved = self.left_wait_time > STARVATION_LIMIT
        right_starved = self.right_wait_time > STARVATION_LIMIT
        
        if left_starved and not right_starved:
            left_green += starvation_boost
            self.performance_metrics['starvation_events'] += 1
        elif right_starved and not left_starved:
            right_green += starvation_boost
            self.performance_metrics['starvation_events'] += 1
        elif left_starved and right_starved:
            # Both sides starved - prioritize by wait time
            if self.left_wait_time > self.right_wait_time:
                left_green += starvation_boost
            else:
                right_green += starvation_boost
                
        # Apply aging factor for gradual priority increase
        aging_left = self.aging_factor * (self.left_wait_time / STARVATION_LIMIT)
        aging_right = self.aging_factor * (self.right_wait_time / STARVATION_LIMIT)
        
        left_green += aging_left * 5
        right_green += aging_right * 5
        
        return left_green, right_green
        
    def _apply_fairness_adjustment(self, left_green, right_green, left_queue, right_queue):
        """Apply fairness adjustments based on queue lengths"""
        if left_queue == 0 and right_queue == 0:
            return left_green, right_green
            
        # Adjust based on queue imbalance
        total_queue = left_queue + right_queue
        if total_queue > 0:
            queue_ratio_left = left_queue / total_queue
            queue_ratio_right = right_queue / total_queue
            
            # Boost green time for side with longer queue
            if queue_ratio_left > 0.7:  # Left side has much longer queue
                left_green += 5
            elif queue_ratio_right > 0.7:  # Right side has much longer queue
                right_green += 5
                
        return left_green, right_green
        
    def _record_decision(self, left_vehicles, right_vehicles, left_green, right_green):
        """Record AI decision for analysis and learning"""
        decision = {
            'timestamp': time.time(),
            'left_count': len([v for v in left_vehicles if v.is_in_detection_zone()]),
            'right_count': len([v for v in right_vehicles if v.is_in_detection_zone()]),
            'left_weighted': self._calculate_weighted_count(left_vehicles),
            'right_weighted': self._calculate_weighted_count(right_vehicles),
            'left_wait_time': self.left_wait_time,
            'right_wait_time': self.right_wait_time,
            'left_green_assigned': left_green,
            'right_green_assigned': right_green,
            'starvation_left': self.left_wait_time > STARVATION_LIMIT,
            'starvation_right': self.right_wait_time > STARVATION_LIMIT
        }
        
        self.decision_history.append(decision)
        
        # Keep history manageable
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-50:]
            
    def get_ai_status(self):
        """Get current AI system status and metrics"""
        return {
            'left_wait_time': self.left_wait_time,
            'right_wait_time': self.right_wait_time,
            'starvation_risk_left': self.left_wait_time / STARVATION_LIMIT,
            'starvation_risk_right': self.right_wait_time / STARVATION_LIMIT,
            'decisions_made': len(self.decision_history),
            'performance_metrics': self.performance_metrics.copy()
        }
        
    def get_recommendation_reason(self, left_vehicles, right_vehicles):
        """Get human-readable explanation of AI decision"""
        left_count = self._calculate_weighted_count(left_vehicles)
        right_count = self._calculate_weighted_count(right_vehicles)
        left_queue = self._calculate_queue_length(left_vehicles)
        right_queue = self._calculate_queue_length(right_vehicles)
        
        reasons = []
        
        # Traffic demand analysis
        if left_count > right_count * 1.5:
            reasons.append(f"Heavy traffic on Feroke side ({left_count:.1f} PCU vs {right_count:.1f} PCU)")
        elif right_count > left_count * 1.5:
            reasons.append(f"Heavy traffic on Ramanattukara side ({right_count:.1f} PCU vs {left_count:.1f} PCU)")
        else:
            reasons.append(f"Balanced traffic (L:{left_count:.1f}, R:{right_count:.1f} PCU)")
            
        # Queue analysis
        if left_queue > right_queue + 2:
            reasons.append(f"Long queue on Feroke side ({left_queue} vehicles)")
        elif right_queue > left_queue + 2:
            reasons.append(f"Long queue on Ramanattukara side ({right_queue} vehicles)")
            
        # Starvation analysis
        if self.left_wait_time > STARVATION_LIMIT * 0.8:
            reasons.append(f"Feroke side approaching starvation ({self.left_wait_time:.1f}s wait)")
        if self.right_wait_time > STARVATION_LIMIT * 0.8:
            reasons.append(f"Ramanattukara side approaching starvation ({self.right_wait_time:.1f}s wait)")
            
        return reasons if reasons else ["Normal traffic conditions"]
        
    def update_performance_metrics(self, vehicles_passed_left, vehicles_passed_right, 
                                 avg_wait_left, avg_wait_right):
        """Update performance metrics for analysis"""
        self.performance_metrics['total_vehicles_left'] += vehicles_passed_left
        self.performance_metrics['total_vehicles_right'] += vehicles_passed_right
        self.performance_metrics['total_wait_time_left'] += avg_wait_left
        self.performance_metrics['total_wait_time_right'] += avg_wait_right
        
    def reset_metrics(self):
        """Reset all performance metrics"""
        self.performance_metrics = {
            'total_vehicles_left': 0,
            'total_vehicles_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'cycles_completed': 0,
            'starvation_events': 0
        }
        self.left_wait_time = 0
        self.right_wait_time = 0
        self.decision_history.clear()
