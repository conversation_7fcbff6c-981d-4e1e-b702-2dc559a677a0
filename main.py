"""
Smart Autonomous AI-Driven Signal System for Feroke Bridge
Main simulation file - Pygame implementation

This simulation demonstrates an AI-controlled traffic signal system
for the narrow Old Feroke Bridge in Kozhikode, Kerala.
"""

import pygame
import sys
import random
import time
from vehicle import Vehicle
from signal import TrafficSignal
from traffic_ai import TrafficAI
from utils import *

class FerokeTrafficSimulation:
    """Main simulation class for the Feroke Bridge traffic system"""
    
    def __init__(self):
        """Initialize the simulation"""
        pygame.init()
        
        # Display setup
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Smart Feroke Bridge Traffic Control System")
        self.clock = pygame.time.Clock()
        
        # Fonts
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
        # Core systems
        self.traffic_signal = TrafficSignal()
        self.traffic_ai = TrafficAI()
        
        # Vehicle management
        self.left_vehicles = []   # Vehicles on Feroke side
        self.right_vehicles = []  # Vehicles on Ramanattukara side
        self.removed_vehicles = []  # For statistics
        
        # Simulation state
        self.running = True
        self.paused = False
        self.simulation_time = 0
        self.last_spawn_time = {"LEFT": 0, "RIGHT": 0}
        
        # Statistics
        self.stats = {
            'vehicles_spawned_left': 0,
            'vehicles_spawned_right': 0,
            'vehicles_completed_left': 0,
            'vehicles_completed_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'avg_wait_time_left': 0,
            'avg_wait_time_right': 0
        }
        
        # Performance tracking
        self.fps_counter = 0
        self.fps_timer = 0
        self.current_fps = 60
        
    def run(self):
        """Main simulation loop"""
        print("Starting Feroke Bridge Traffic Simulation...")
        print("Controls:")
        print("  SPACE - Pause/Resume")
        print("  R - Reset simulation")
        print("  1 - Force left green (manual override)")
        print("  2 - Force right green (manual override)")
        print("  ESC - Exit")
        
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds
            
            if not self.paused:
                self.simulation_time += dt
                
            self._handle_events()
            
            if not self.paused:
                self._update_simulation(dt)
                
            self._render()
            
            # FPS tracking
            self._update_fps_counter(dt)
            
        pygame.quit()
        sys.exit()
        
    def _handle_events(self):
        """Handle user input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
                
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                elif event.key == pygame.K_r:
                    self._reset_simulation()
                elif event.key == pygame.K_1:
                    # Manual override - force left green
                    self.traffic_signal.set_manual_override(TrafficSignal.LEFT_GREEN)
                elif event.key == pygame.K_2:
                    # Manual override - force right green
                    self.traffic_signal.set_manual_override(TrafficSignal.RIGHT_GREEN)
                elif event.key == pygame.K_c:
                    # Clear manual override
                    self.traffic_signal.clear_manual_override()
                    
    def _update_simulation(self, dt):
        """Update all simulation components"""
        # Spawn new vehicles
        self._spawn_vehicles(dt)
        
        # Update AI waiting times
        self.traffic_ai.update_waiting_times(self.traffic_signal.current_state, dt)
        
        # Calculate new signal timing based on current traffic
        left_green_time, right_green_time = self.traffic_ai.calculate_green_times(
            self.left_vehicles, self.right_vehicles
        )
        
        # Update traffic signal
        self.traffic_signal.update(left_green_time, right_green_time)
        
        # Update all vehicles
        self._update_vehicles(dt)
        
        # Remove completed vehicles and update statistics
        self._cleanup_vehicles()
        
        # Update statistics
        self._update_statistics()
        
    def _spawn_vehicles(self, dt):
        """Spawn new vehicles based on probabilities"""
        current_time = self.simulation_time
        
        # Spawn on left side (Feroke)
        if current_time - self.last_spawn_time["LEFT"] > 1.0:  # Check every second
            for vehicle_type, probability in SPAWN_PROBABILITIES.items():
                if random.random() < probability * dt:
                    vehicle = Vehicle(vehicle_type, "LEFT", LEFT_SPAWN_X)
                    self.left_vehicles.append(vehicle)
                    self.stats['vehicles_spawned_left'] += 1
                    self.last_spawn_time["LEFT"] = current_time
                    break  # Only spawn one vehicle per check
                    
        # Spawn on right side (Ramanattukara)
        if current_time - self.last_spawn_time["RIGHT"] > 1.0:
            for vehicle_type, probability in SPAWN_PROBABILITIES.items():
                if random.random() < probability * dt:
                    vehicle = Vehicle(vehicle_type, "RIGHT", RIGHT_SPAWN_X)
                    self.right_vehicles.append(vehicle)
                    self.stats['vehicles_spawned_right'] += 1
                    self.last_spawn_time["RIGHT"] = current_time
                    break
                    
    def _update_vehicles(self, dt):
        """Update all vehicle positions and states"""
        # Update left side vehicles
        for vehicle in self.left_vehicles:
            vehicle.update(dt, self.traffic_signal.current_state, self.left_vehicles)
            
        # Update right side vehicles
        for vehicle in self.right_vehicles:
            vehicle.update(dt, self.traffic_signal.current_state, self.right_vehicles)
            
    def _cleanup_vehicles(self):
        """Remove vehicles that have completed their journey"""
        # Check left side vehicles
        completed_left = []
        for vehicle in self.left_vehicles[:]:
            if vehicle.should_be_removed():
                completed_left.append(vehicle)
                self.left_vehicles.remove(vehicle)
                self.stats['vehicles_completed_left'] += 1
                self.stats['total_wait_time_left'] += vehicle.total_wait_time
                
        # Check right side vehicles
        completed_right = []
        for vehicle in self.right_vehicles[:]:
            if vehicle.should_be_removed():
                completed_right.append(vehicle)
                self.right_vehicles.remove(vehicle)
                self.stats['vehicles_completed_right'] += 1
                self.stats['total_wait_time_right'] += vehicle.total_wait_time
                
        # Store completed vehicles for analysis
        self.removed_vehicles.extend(completed_left + completed_right)
        
    def _update_statistics(self):
        """Update simulation statistics"""
        # Calculate average wait times
        if self.stats['vehicles_completed_left'] > 0:
            self.stats['avg_wait_time_left'] = (
                self.stats['total_wait_time_left'] / self.stats['vehicles_completed_left']
            )
            
        if self.stats['vehicles_completed_right'] > 0:
            self.stats['avg_wait_time_right'] = (
                self.stats['total_wait_time_right'] / self.stats['vehicles_completed_right']
            )
            
    def _reset_simulation(self):
        """Reset the simulation to initial state"""
        self.left_vehicles.clear()
        self.right_vehicles.clear()
        self.removed_vehicles.clear()
        
        self.traffic_signal = TrafficSignal()
        self.traffic_ai = TrafficAI()
        
        self.simulation_time = 0
        self.last_spawn_time = {"LEFT": 0, "RIGHT": 0}
        
        # Reset statistics
        self.stats = {
            'vehicles_spawned_left': 0,
            'vehicles_spawned_right': 0,
            'vehicles_completed_left': 0,
            'vehicles_completed_right': 0,
            'total_wait_time_left': 0,
            'total_wait_time_right': 0,
            'avg_wait_time_left': 0,
            'avg_wait_time_right': 0
        }
        
        print("Simulation reset")
        
    def _update_fps_counter(self, dt):
        """Update FPS counter"""
        self.fps_counter += 1
        self.fps_timer += dt
        
        if self.fps_timer >= 1.0:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.fps_timer = 0
            
    def _render(self):
        """Render the entire simulation"""
        # Clear screen
        self.screen.fill(BACKGROUND_COLOR)
        
        # Draw environment
        self._draw_environment()
        
        # Draw vehicles
        self._draw_vehicles()
        
        # Draw traffic signals
        self.traffic_signal.render(self.screen, self.font_medium)
        
        # Draw UI and statistics
        self._draw_ui()
        
        # Update display
        pygame.display.flip()
        
    def _draw_environment(self):
        """Draw the bridge environment"""
        # Draw water under bridge
        water_rect = pygame.Rect(BRIDGE_X - 50, BRIDGE_Y + BRIDGE_HEIGHT, 
                                BRIDGE_WIDTH + 100, 100)
        pygame.draw.rect(self.screen, WATER_COLOR, water_rect)
        
        # Draw bridge
        bridge_rect = pygame.Rect(BRIDGE_X, BRIDGE_Y, BRIDGE_WIDTH, BRIDGE_HEIGHT)
        pygame.draw.rect(self.screen, BRIDGE_COLOR, bridge_rect)
        
        # Draw approach roads
        left_road = pygame.Rect(0, BRIDGE_Y, BRIDGE_X, BRIDGE_HEIGHT)
        right_road = pygame.Rect(BRIDGE_X + BRIDGE_WIDTH, BRIDGE_Y, 
                               SCREEN_WIDTH - BRIDGE_X - BRIDGE_WIDTH, BRIDGE_HEIGHT)
        pygame.draw.rect(self.screen, ROAD_COLOR, left_road)
        pygame.draw.rect(self.screen, ROAD_COLOR, right_road)
        
        # Draw lane markings
        lane_y = BRIDGE_Y + BRIDGE_HEIGHT // 2
        for x in range(0, SCREEN_WIDTH, 40):
            pygame.draw.rect(self.screen, (255, 255, 255), (x, lane_y - 1, 20, 2))
            
        # Draw stop lines
        pygame.draw.rect(self.screen, (255, 255, 0), 
                        (LEFT_STOP_LINE, BRIDGE_Y, 3, BRIDGE_HEIGHT))
        pygame.draw.rect(self.screen, (255, 255, 0), 
                        (RIGHT_STOP_LINE, BRIDGE_Y, 3, BRIDGE_HEIGHT))
        
    def _draw_vehicles(self):
        """Draw all vehicles"""
        for vehicle in self.left_vehicles + self.right_vehicles:
            vehicle.render(self.screen)

    def _draw_ui(self):
        """Draw user interface and statistics"""
        # Draw UI background panel
        ui_rect = pygame.Rect(0, UI_PANEL_Y, SCREEN_WIDTH, UI_PANEL_HEIGHT)
        pygame.draw.rect(self.screen, UI_BACKGROUND, ui_rect)
        pygame.draw.rect(self.screen, UI_ACCENT, ui_rect, width=2)

        # Title
        title_text = "Smart Feroke Bridge Traffic Control System"
        draw_text(self.screen, title_text, (SCREEN_WIDTH // 2, 20),
                 self.font_large, UI_ACCENT, center=True)

        # Current statistics
        self._draw_statistics_panel()

        # AI status
        self._draw_ai_status_panel()

        # Vehicle counts
        self._draw_vehicle_counts_panel()

        # Controls help
        self._draw_controls_panel()

        # Performance info
        self._draw_performance_info()

    def _draw_statistics_panel(self):
        """Draw main statistics panel"""
        y_start = UI_PANEL_Y + 10
        x_left = 20

        # Left side statistics
        draw_text(self.screen, "FEROKE SIDE", (x_left, y_start),
                 self.font_medium, UI_ACCENT)
        draw_text(self.screen, f"Spawned: {self.stats['vehicles_spawned_left']}",
                 (x_left, y_start + 25), self.font_small, UI_TEXT)
        draw_text(self.screen, f"Completed: {self.stats['vehicles_completed_left']}",
                 (x_left, y_start + 45), self.font_small, UI_TEXT)
        draw_text(self.screen, f"Avg Wait: {self.stats['avg_wait_time_left']:.1f}s",
                 (x_left, y_start + 65), self.font_small, UI_TEXT)

        # Current queue
        left_queue = len([v for v in self.left_vehicles if v.is_in_detection_zone()])
        draw_text(self.screen, f"Queue: {left_queue}",
                 (x_left, y_start + 85), self.font_small, UI_SUCCESS if left_queue < 5 else UI_WARNING)

        # Right side statistics
        x_right = SCREEN_WIDTH - 200
        draw_text(self.screen, "RAMANATTUKARA SIDE", (x_right, y_start),
                 self.font_medium, UI_ACCENT)
        draw_text(self.screen, f"Spawned: {self.stats['vehicles_spawned_right']}",
                 (x_right, y_start + 25), self.font_small, UI_TEXT)
        draw_text(self.screen, f"Completed: {self.stats['vehicles_completed_right']}",
                 (x_right, y_start + 45), self.font_small, UI_TEXT)
        draw_text(self.screen, f"Avg Wait: {self.stats['avg_wait_time_right']:.1f}s",
                 (x_right, y_start + 65), self.font_small, UI_TEXT)

        # Current queue
        right_queue = len([v for v in self.right_vehicles if v.is_in_detection_zone()])
        draw_text(self.screen, f"Queue: {right_queue}",
                 (x_right, y_start + 85), self.font_small, UI_SUCCESS if right_queue < 5 else UI_WARNING)

    def _draw_ai_status_panel(self):
        """Draw AI system status"""
        x_center = SCREEN_WIDTH // 2 - 100
        y_start = UI_PANEL_Y + 10

        draw_text(self.screen, "AI CONTROL STATUS", (x_center, y_start),
                 self.font_medium, UI_ACCENT)

        ai_status = self.traffic_ai.get_ai_status()

        # Wait times with starvation risk indicators
        left_risk = ai_status['starvation_risk_left']
        right_risk = ai_status['starvation_risk_right']

        left_color = UI_WARNING if left_risk > 0.8 else UI_SUCCESS if left_risk < 0.5 else UI_TEXT
        right_color = UI_WARNING if right_risk > 0.8 else UI_SUCCESS if right_risk < 0.5 else UI_TEXT

        draw_text(self.screen, f"Left Wait: {ai_status['left_wait_time']:.1f}s",
                 (x_center, y_start + 25), self.font_small, left_color)
        draw_text(self.screen, f"Right Wait: {ai_status['right_wait_time']:.1f}s",
                 (x_center, y_start + 45), self.font_small, right_color)

        # AI recommendations
        reasons = self.traffic_ai.get_recommendation_reason(self.left_vehicles, self.right_vehicles)
        if reasons:
            draw_text(self.screen, "AI Analysis:", (x_center, y_start + 65),
                     self.font_small, UI_ACCENT)
            draw_text(self.screen, reasons[0][:40] + "..." if len(reasons[0]) > 40 else reasons[0],
                     (x_center, y_start + 85), self.font_small, UI_TEXT)

    def _draw_vehicle_counts_panel(self):
        """Draw vehicle type breakdown"""
        y_pos = UI_PANEL_Y + 110
        x_start = 20

        # Count vehicles by type for left side
        left_counts = {}
        for vehicle in self.left_vehicles:
            if vehicle.is_in_detection_zone():
                left_counts[vehicle.type] = left_counts.get(vehicle.type, 0) + 1

        # Count vehicles by type for right side
        right_counts = {}
        for vehicle in self.right_vehicles:
            if vehicle.is_in_detection_zone():
                right_counts[vehicle.type] = right_counts.get(vehicle.type, 0) + 1

        # Draw vehicle type counts
        x_offset = 0
        for vehicle_type in VEHICLE_WEIGHTS.keys():
            color = VEHICLE_COLORS[vehicle_type]
            left_count = left_counts.get(vehicle_type, 0)
            right_count = right_counts.get(vehicle_type, 0)

            # Draw colored indicator
            pygame.draw.rect(self.screen, color, (x_start + x_offset, y_pos, 15, 10))
            draw_text(self.screen, f"{vehicle_type.upper()}",
                     (x_start + x_offset + 20, y_pos), self.font_small, UI_TEXT)
            draw_text(self.screen, f"L:{left_count} R:{right_count}",
                     (x_start + x_offset + 20, y_pos + 15), self.font_small, UI_TEXT)

            x_offset += 120

    def _draw_controls_panel(self):
        """Draw control instructions"""
        x_pos = SCREEN_WIDTH - 300
        y_pos = UI_PANEL_Y + 110

        draw_text(self.screen, "CONTROLS", (x_pos, y_pos), self.font_small, UI_ACCENT)
        controls = [
            "SPACE: Pause/Resume",
            "R: Reset",
            "1/2: Manual Override",
            "C: Clear Override"
        ]

        for i, control in enumerate(controls):
            draw_text(self.screen, control, (x_pos, y_pos + 15 + i * 15),
                     self.font_small, UI_TEXT)

    def _draw_performance_info(self):
        """Draw performance information"""
        # Simulation time
        time_text = f"Time: {format_time(self.simulation_time)}"
        draw_text(self.screen, time_text, (SCREEN_WIDTH - 150, 50),
                 self.font_small, UI_TEXT)

        # FPS
        fps_text = f"FPS: {self.current_fps}"
        fps_color = UI_SUCCESS if self.current_fps >= 55 else UI_WARNING
        draw_text(self.screen, fps_text, (SCREEN_WIDTH - 150, 70),
                 self.font_small, fps_color)

        # Pause indicator
        if self.paused:
            pause_text = "PAUSED"
            draw_text(self.screen, pause_text, (SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2),
                     self.font_large, UI_WARNING, center=True)


def main():
    """Main entry point for the simulation"""
    try:
        simulation = FerokeTrafficSimulation()
        simulation.run()
    except Exception as e:
        print(f"Error running simulation: {e}")
        pygame.quit()
        sys.exit(1)


if __name__ == "__main__":
    main()
