#!/usr/bin/env python3
"""
Enhanced AI Demonstration Script
Shows the new intelligent features of the Feroke Bridge Traffic Control System
"""

import pygame
import sys
from traffic_ai import TrafficAI
from vehicle import Vehicle
from utils import *

def demo_enhanced_ai():
    """Demonstrate the enhanced AI capabilities"""
    print("🧠 ENHANCED AI DEMONSTRATION")
    print("=" * 50)
    
    # Initialize AI
    ai = TrafficAI()
    print(f"✅ AI Initialized")
    print(f"   Decision Interval: {ai.decision_interval}s")
    print(f"   Max Wait Time: {ai.max_wait_time}s")
    print(f"   Starvation Boost: {ai.starvation_boost}s")
    print(f"   Emergency Threshold: {ai.emergency_threshold}")
    
    # Create test scenarios
    scenarios = [
        {
            'name': 'Balanced Traffic',
            'left': [Vehicle('car', 'LEFT', 100), Vehicle('bike', 'LEFT', 150)],
            'right': [Vehicle('car', 'RIGHT', 1100), Vehicle('auto', 'RIGHT', 1150)]
        },
        {
            'name': 'Heavy Left Traffic',
            'left': [Vehicle('truck', 'LEFT', 100), Vehicle('bus', 'LEFT', 150), Vehicle('car', 'LEFT', 200)],
            'right': [Vehicle('bike', 'RIGHT', 1100)]
        },
        {
            'name': 'Emergency Scenario',
            'left': [Vehicle('car', 'LEFT', 100)],
            'right': [Vehicle('truck', 'RIGHT', 1100), Vehicle('bus', 'RIGHT', 1150)]
        }
    ]
    
    print(f"\n🎯 TESTING {len(scenarios)} SCENARIOS")
    print("-" * 50)
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   Left: {len(scenario['left'])} vehicles")
        print(f"   Right: {len(scenario['right'])} vehicles")
        
        # Simulate starvation for emergency scenario
        if 'Emergency' in scenario['name']:
            ai.right_wait_time = 95  # Near emergency threshold
        
        # Force new decision
        ai.last_decision_time = 0
        
        # Make AI decision
        decision = ai.make_intelligent_decision(
            scenario['left'], 
            scenario['right'], 
            'LEFT_GREEN'
        )
        
        if decision:
            print(f"   🎯 AI Decision:")
            print(f"      Priority: {decision['priority_side']} ({decision['urgency_level']})")
            print(f"      Green Times: L={decision['left_green_time']}s, R={decision['right_green_time']}s")
            
            if decision['reasoning']:
                print(f"      Reasoning:")
                for reason in decision['reasoning'][:2]:  # Show first 2 reasons
                    print(f"        • {reason}")
        
        # Reset for next scenario
        ai.left_wait_time = 0
        ai.right_wait_time = 0
    
    print(f"\n📊 PERFORMANCE METRICS")
    print("-" * 50)
    metrics = ai.performance_metrics
    print(f"   Decisions Made: {metrics['decisions_made']}")
    print(f"   Emergency Interventions: {metrics['emergency_interventions']}")
    print(f"   Starvation Events: {metrics['starvation_events']}")
    
    print(f"\n✅ ENHANCED AI DEMONSTRATION COMPLETE!")
    print("   The AI system is ready for intelligent traffic control!")

def demo_manual_vehicle_addition():
    """Demonstrate manual vehicle addition feature"""
    print(f"\n🚗 MANUAL VEHICLE ADDITION DEMO")
    print("=" * 50)
    
    ai = TrafficAI()
    
    print("   Manual vehicle addition allows testing specific scenarios:")
    print("   • Click buttons in the GUI to add vehicles")
    print("   • Test AI response to different traffic patterns")
    print("   • Validate starvation prevention algorithms")
    
    # Simulate manual addition
    ai.add_manual_vehicle('LEFT', 'truck')
    print(f"   ✅ Manual truck added to LEFT side")
    print(f"   ✅ AI notified and ready for immediate decision")

if __name__ == "__main__":
    try:
        # Initialize pygame (required for vehicle creation)
        pygame.init()
        
        demo_enhanced_ai()
        demo_manual_vehicle_addition()
        
        print(f"\n🌉 FEROKE BRIDGE ENHANCED AI SYSTEM")
        print("   Ready for intelligent traffic management!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        sys.exit(1)
    finally:
        pygame.quit()
