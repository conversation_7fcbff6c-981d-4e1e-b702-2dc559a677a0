"""
Test script to verify the Feroke Bridge Traffic Simulation
This script tests core functionality without running the full GUI
"""

import sys
import time
from vehicle import Vehicle
from signal import TrafficSignal
from traffic_ai import TrafficAI
from utils import *

def test_vehicle_creation():
    """Test vehicle creation and basic properties"""
    print("Testing vehicle creation...")
    
    # Test different vehicle types
    vehicle_types = ["bike", "car", "auto", "truck", "bus"]
    
    for vtype in vehicle_types:
        vehicle = Vehicle(vtype, "LEFT", 100)
        assert vehicle.type == vtype
        assert vehicle.side == "LEFT"
        assert vehicle.weight == VEHICLE_WEIGHTS[vtype]
        assert vehicle.max_speed == VEHICLE_SPEEDS[vtype]
        print(f"  ✓ {vtype.upper()}: weight={vehicle.weight}, speed={vehicle.max_speed}")
    
    print("Vehicle creation test passed!\n")

def test_signal_system():
    """Test traffic signal state management"""
    print("Testing signal system...")
    
    signal = TrafficSignal()
    initial_state = signal.current_state
    print(f"  Initial state: {initial_state}")
    
    # Test state transitions
    signal.update(10, 10)  # Update with 10 second green times
    print(f"  State after update: {signal.current_state}")
    
    # Test manual override
    signal.set_manual_override(TrafficSignal.RIGHT_GREEN)
    assert signal.manual_override == True
    print("  ✓ Manual override works")
    
    signal.clear_manual_override()
    assert signal.manual_override == False
    print("  ✓ Override clearing works")
    
    print("Signal system test passed!\n")

def test_ai_system():
    """Test AI traffic control logic"""
    print("Testing AI system...")
    
    ai = TrafficAI()
    
    # Create test vehicles
    left_vehicles = [
        Vehicle("car", "LEFT", 100),
        Vehicle("truck", "LEFT", 150),
        Vehicle("bike", "LEFT", 200)
    ]
    
    right_vehicles = [
        Vehicle("car", "RIGHT", 1000),
        Vehicle("bus", "RIGHT", 1050)
    ]
    
    # Test AI decision making
    decision = ai.make_intelligent_decision(left_vehicles, right_vehicles, "LEFT_GREEN")
    if decision:
        left_green = decision['left_green_time']
        right_green = decision['right_green_time']
        print(f"  AI Decision: Left={left_green}s, Right={right_green}s")
        print(f"  Priority: {decision['priority_side']}, Urgency: {decision['urgency_level']}")

        assert MIN_GREEN <= left_green <= MAX_GREEN
        assert MIN_GREEN <= right_green <= MAX_GREEN
        print("  ✓ Green times within valid range")

        # Test starvation prevention
        ai.left_wait_time = STARVATION_LIMIT + 10  # Simulate starvation
        ai.last_decision_time = 0  # Force new decision
        decision_starved = ai.make_intelligent_decision(left_vehicles, right_vehicles, "LEFT_GREEN")
        if decision_starved:
            left_green_starved = decision_starved['left_green_time']
            print(f"  With starvation: Left={left_green_starved}s")

            assert left_green_starved >= left_green  # Should get priority boost
            print("  ✓ Starvation prevention works")
    else:
        print("  ✓ AI decision system initialized")
    
    print("AI system test passed!\n")

def test_vehicle_physics():
    """Test vehicle movement and physics"""
    print("Testing vehicle physics...")
    
    vehicle = Vehicle("car", "LEFT", 100)
    initial_pos = vehicle.pos[0]
    
    # Simulate movement for 1 second with green signal
    dt = 1.0
    vehicle.update(dt, TrafficSignal.LEFT_GREEN, [])
    
    # Vehicle should have moved
    assert vehicle.pos[0] > initial_pos
    print(f"  ✓ Vehicle moved from {initial_pos} to {vehicle.pos[0]}")
    
    # Test stopping at red signal
    red_pos = vehicle.pos[0]
    vehicle.update(dt, TrafficSignal.RIGHT_GREEN, [])  # Red for left side
    
    # Vehicle should slow down/stop
    print(f"  ✓ Vehicle behavior at red signal: velocity={vehicle.velocity:.1f}")
    
    print("Vehicle physics test passed!\n")

def test_configuration():
    """Test configuration constants"""
    print("Testing configuration...")
    
    # Test vehicle weights sum to reasonable values
    total_weight = sum(VEHICLE_WEIGHTS.values())
    print(f"  Total vehicle weights: {total_weight}")
    
    # Test spawn probabilities
    total_spawn_prob = sum(SPAWN_PROBABILITIES.values())
    print(f"  Total spawn probability: {total_spawn_prob}")
    
    # Test timing constants
    assert MIN_GREEN < MAX_GREEN
    assert YELLOW_TIME > 0
    assert STARVATION_LIMIT > MAX_GREEN
    print("  ✓ Timing constants are logical")
    
    print("Configuration test passed!\n")

def run_simulation_test():
    """Run a brief simulation test"""
    print("Running brief simulation test...")
    
    # Initialize systems
    signal = TrafficSignal()
    ai = TrafficAI()
    vehicles_left = []
    vehicles_right = []
    
    # Simulate for 10 cycles
    for cycle in range(10):
        # Add some test vehicles
        if cycle % 3 == 0:
            vehicles_left.append(Vehicle("car", "LEFT", 50))
        if cycle % 4 == 0:
            vehicles_right.append(Vehicle("truck", "RIGHT", 1200))
        
        # Update AI
        ai.update_waiting_times(signal.current_state, 1.0)

        # Make AI decision (force new decision each cycle for testing)
        ai.last_decision_time = 0
        decision = ai.make_intelligent_decision(vehicles_left, vehicles_right, signal.current_state)

        if decision:
            left_green = decision['left_green_time']
            right_green = decision['right_green_time']
        else:
            left_green = MIN_GREEN
            right_green = MIN_GREEN

        # Update signal
        signal.update(left_green, right_green)
        
        # Update vehicles
        for vehicle in vehicles_left + vehicles_right:
            vehicle.update(1.0, signal.current_state, vehicles_left + vehicles_right)
        
        print(f"  Cycle {cycle+1}: State={signal.current_state}, "
              f"Left={len(vehicles_left)}, Right={len(vehicles_right)}")
    
    print("Simulation test completed!\n")

def main():
    """Run all tests"""
    print("=" * 60)
    print("FEROKE BRIDGE TRAFFIC SIMULATION - TEST SUITE")
    print("=" * 60)
    print()
    
    try:
        test_configuration()
        test_vehicle_creation()
        test_signal_system()
        test_ai_system()
        test_vehicle_physics()
        run_simulation_test()
        
        print("=" * 60)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("The simulation is ready to run.")
        print("Execute 'python main.py' to start the full simulation.")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        print("Please check the code and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
