"""
Test signal timing to debug the state transition issue
"""

import time
from signal import TrafficSignal
from utils import *

def test_signal_transitions():
    """Test signal state transitions"""
    print("Testing signal transitions...")
    
    signal = TrafficSignal()
    print(f"Initial state: {signal.current_state}")
    print(f"Initial duration: {signal.state_duration}")
    
    # Simulate time passing
    for i in range(20):  # 20 seconds
        print(f"Time {i}s: State={signal.current_state}, Remaining={signal.get_remaining_time():.1f}s")
        
        # Update signal every second
        signal.update(10, 10)  # 10 second green times
        
        # Sleep for 1 second
        time.sleep(1)
        
        if i > 15:  # Stop after seeing some transitions
            break
    
    print("Signal transition test completed")

if __name__ == "__main__":
    test_signal_transitions()
